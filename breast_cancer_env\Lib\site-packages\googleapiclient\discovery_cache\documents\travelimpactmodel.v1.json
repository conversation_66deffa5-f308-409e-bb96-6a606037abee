{"basePath": "", "baseUrl": "https://travelimpactmodel.googleapis.com/", "batchPath": "batch", "canonicalName": "Travel Impact Model", "description": "Travel Impact Model API lets you query travel carbon emission estimates.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/travel/impact-model", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "travelimpactmodel:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://travelimpactmodel.mtls.googleapis.com/", "name": "travelimpactmodel", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"flights": {"methods": {"computeFlightEmissions": {"description": "Stateless method to retrieve emission estimates. Details on how emission estimates are computed are in [GitHub](https://github.com/google/travel-impact-model) The response will contain all entries that match the input flight legs, in the same order. If there are no estimates available for a certain flight leg, the response will return the flight leg object with empty emission fields. The request will still be considered successful. Reasons for missing emission estimates include: * The flight is unknown to the server. * The input flight leg is missing one or more identifiers. * The flight date is in the past. * The aircraft type is not supported by the model. * Missing seat configuration. The request can contain up to 1000 flight legs. If the request has more than 1000 direct flights, if will fail with an INVALID_ARGUMENT error.", "flatPath": "v1/flights:computeFlightEmissions", "httpMethod": "POST", "id": "travelimpactmodel.flights.computeFlightEmissions", "parameterOrder": [], "parameters": {}, "path": "v1/flights:computeFlightEmissions", "request": {"$ref": "ComputeFlightEmissionsRequest"}, "response": {"$ref": "ComputeFlightEmissionsResponse"}}, "computeTypicalFlightEmissions": {"description": "Retrieves typical flight emissions estimates between two airports, also known as a market. If there are no estimates available for a certain market, the response will return the market object with empty emission fields. The request will still be considered successful. Details on how the typical emissions estimates are computed are on [GitHub](https://github.com/google/travel-impact-model/blob/main/projects/typical_flight_emissions.md). The request can contain up to 1000 markets. If the request has more than 1000 markets, it will fail with an INVALID_ARGUMENT error.", "flatPath": "v1/flights:computeTypicalFlightEmissions", "httpMethod": "POST", "id": "travelimpactmodel.flights.computeTypicalFlightEmissions", "parameterOrder": [], "parameters": {}, "path": "v1/flights:computeTypicalFlightEmissions", "request": {"$ref": "ComputeTypicalFlightEmissionsRequest"}, "response": {"$ref": "ComputeTypicalFlightEmissionsResponse"}}}}}, "revision": "20250624", "rootUrl": "https://travelimpactmodel.googleapis.com/", "schemas": {"ComputeFlightEmissionsRequest": {"description": "Input definition for the ComputeFlightEmissions request.", "id": "ComputeFlightEmissionsRequest", "properties": {"flights": {"description": "Required. Direct flights to return emission estimates for.", "items": {"$ref": "Flight"}, "type": "array"}}, "type": "object"}, "ComputeFlightEmissionsResponse": {"description": "Output definition for the ComputeFlightEmissions response.", "id": "ComputeFlightEmissionsResponse", "properties": {"flightEmissions": {"description": "List of flight legs with emission estimates.", "items": {"$ref": "FlightWithEmissions"}, "type": "array"}, "modelVersion": {"$ref": "ModelVersion", "description": "The model version under which emission estimates for all flights in this response were computed."}}, "type": "object"}, "ComputeTypicalFlightEmissionsRequest": {"description": "A list of pair of airports (markets) to request the typical emissions for.", "id": "ComputeTypicalFlightEmissionsRequest", "properties": {"markets": {"description": "Required. Request the typical flight emissions estimates for this market pair. A maximum of 1000 markets can be requested.", "items": {"$ref": "Market"}, "type": "array"}}, "type": "object"}, "ComputeTypicalFlightEmissionsResponse": {"description": "The response includes the emissions but also the model version.", "id": "ComputeTypicalFlightEmissionsResponse", "properties": {"modelVersion": {"$ref": "ModelVersion", "description": "The model version under which typical flight emission estimates for all flights in this response were computed."}, "typicalFlightEmissions": {"description": "Market's Typical Flight Emissions requested.", "items": {"$ref": "TypicalFlightEmissions"}, "type": "array"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "EasaLabelMetadata": {"description": "Metadata about the EASA Flight Emissions Label.", "id": "EasaLabelMetadata", "properties": {"labelExpiryDate": {"$ref": "Date", "description": "The date when the label expires. The label can be displayed until the end of this date."}, "labelIssueDate": {"$ref": "Date", "description": "The date when the label was issued."}, "labelVersion": {"description": "Version of the label.", "type": "string"}, "safDiscountPercentage": {"description": "Sustainable Aviation Fuel (SAF) emissions discount percentage applied to the label. It is a percentage as a decimal. The values are in the interval [0,1]. For example, 0.0021 means 0.21%. This discount and reduction in emissions are reported by the EASA label but they are not included in the CO2e estimates distributed by this API.", "format": "double", "type": "number"}}, "type": "object"}, "EmissionsGramsPerPax": {"description": "Grouped emissions per seating class results.", "id": "EmissionsGramsPerPax", "properties": {"business": {"description": "Emissions for one passenger in business class in grams. This field is always computed and populated, regardless of whether the aircraft has business class seats or not.", "format": "int32", "type": "integer"}, "economy": {"description": "Emissions for one passenger in economy class in grams. This field is always computed and populated, regardless of whether the aircraft has economy class seats or not.", "format": "int32", "type": "integer"}, "first": {"description": "Emissions for one passenger in first class in grams. This field is always computed and populated, regardless of whether the aircraft has first class seats or not.", "format": "int32", "type": "integer"}, "premiumEconomy": {"description": "Emissions for one passenger in premium economy class in grams. This field is always computed and populated, regardless of whether the aircraft has premium economy class seats or not.", "format": "int32", "type": "integer"}}, "type": "object"}, "Flight": {"description": "All details related to a single request item for a direct flight emission estimates.", "id": "Flight", "properties": {"departureDate": {"$ref": "Date", "description": "Required. Date of the flight in the time zone of the origin airport. Must be a date in the present or future."}, "destination": {"description": "Required. IATA airport code for flight destination, e.g. \"JFK\".", "type": "string"}, "flightNumber": {"description": "Required. Flight number, e.g. 324.", "format": "int32", "type": "integer"}, "operatingCarrierCode": {"description": "Required. IATA carrier code, e.g. \"AA\".", "type": "string"}, "origin": {"description": "Required. IATA airport code for flight origin, e.g. \"LHR\".", "type": "string"}}, "type": "object"}, "FlightWithEmissions": {"description": "Direct flight with emission estimates.", "id": "FlightWithEmissions", "properties": {"contrailsImpactBucket": {"description": "Optional. The significance of contrails warming impact compared to the total CO2e emissions impact.", "enum": ["CONTRAILS_IMPACT_UNSPECIFIED", "CONTRAILS_IMPACT_NEGLIGIBLE", "CONTRAILS_IMPACT_MODERATE", "CONTRAILS_IMPACT_SEVERE"], "enumDescriptions": ["The contrails impact is unspecified.", "The contrails impact is negligible compared to the total CO2e emissions.", "The contrails impact is comparable to the total CO2e emissions.", "The contrails impact is higher than the total CO2e emissions impact."], "type": "string"}, "easaLabelMetadata": {"$ref": "EasaLabelMetadata", "description": "Optional. Metadata about the EASA Flight Emissions Label. Only set when the emissions data source is EASA."}, "emissionsGramsPerPax": {"$ref": "EmissionsGramsPerPax", "description": "Optional. Per-passenger emission estimate numbers. Will not be present if emissions could not be computed. For the list of reasons why emissions could not be computed, see ComputeFlightEmissions."}, "flight": {"$ref": "Flight", "description": "Required. Matches the flight identifiers in the request. Note: all IATA codes are capitalized."}, "source": {"description": "Optional. The source of the emissions data.", "enum": ["SOURCE_UNSPECIFIED", "TIM", "EASA"], "enumDescriptions": ["The source of the emissions data is unspecified.", "The emissions data is from the Travel Impact Model.", "The emissions data is from the EASA environmental labels."], "type": "string"}}, "type": "object"}, "Market": {"description": "A pair of airports.", "id": "Market", "properties": {"destination": {"description": "Required. IATA airport code for flight destination, e.g. \"JFK\".", "type": "string"}, "origin": {"description": "Required. IATA airport code for flight origin, e.g. \"LHR\".", "type": "string"}}, "type": "object"}, "ModelVersion": {"description": "Travel Impact Model version. For more information about the model versioning see [GitHub](https://github.com/google/travel-impact-model/#versioning).", "id": "ModelVersion", "properties": {"dated": {"description": "Dated versions: Model datasets are recreated with refreshed input data but no change to the algorithms regularly.", "type": "string"}, "major": {"description": "Major versions: Major changes to methodology (e.g. adding new data sources to the model that lead to major output changes). Such changes will be infrequent and announced well in advance. Might involve API version changes, which will respect [Google Cloud API guidelines](https://cloud.google.com/endpoints/docs/openapi/versioning-an-api#backwards-incompatible)", "format": "int32", "type": "integer"}, "minor": {"description": "Minor versions: Changes to the model that, while being consistent across schema versions, change the model parameters or implementation.", "format": "int32", "type": "integer"}, "patch": {"description": "Patch versions: Implementation changes meant to address bugs or inaccuracies in the model implementation.", "format": "int32", "type": "integer"}}, "type": "object"}, "TypicalFlightEmissions": {"description": "Typical flight emission estimates for a certain market", "id": "TypicalFlightEmissions", "properties": {"emissionsGramsPerPax": {"$ref": "EmissionsGramsPerPax", "description": "Optional. Typical flight emissions per passenger for requested market. Will not be present if a typical emissions could not be computed. For the list of reasons why typical flight emissions could not be computed, see [GitHub](https://github.com/google/travel-impact-model/blob/main/projects/typical_flight_emissions.md#step-7-validate-dataset)."}, "market": {"$ref": "Market", "description": "Required. Matches the flight identifiers in the request. Note: all IATA codes are capitalized."}}, "type": "object"}}, "servicePath": "", "title": "Travel Impact Model API", "version": "v1", "version_module": true}