#!/usr/bin/env python
"""
Test script for Gemini integration in the breast cancer detection system
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.append(str(project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'breast_cancer_detection.settings')
django.setup()

from detection.gemini_service import gemini_service
from detection.models import UploadedImage, PredictionResult

def test_gemini_service():
    """Test the Gemini service functionality"""
    print("🧪 Testing Gemini Image Analysis Service")
    print("=" * 50)
    
    # Test service status
    status = gemini_service.get_service_status()
    print(f"📊 Service Status:")
    print(f"   Gemini Available: {status['gemini_available']}")
    print(f"   Model Initialized: {status['model_initialized']}")
    print(f"   API Key Configured: {status['api_key_configured']}")
    print()
    
    # Test with existing uploaded images
    uploaded_images = UploadedImage.objects.all()
    
    if not uploaded_images.exists():
        print("⚠️  No uploaded images found in database.")
        print("   Please upload an image through the web interface first.")
        return
    
    # Test with the most recent image
    test_image = uploaded_images.last()
    print(f"🖼️  Testing with image: {test_image.original_filename}")
    print(f"   Image ID: {test_image.id}")
    print(f"   Upload time: {test_image.uploaded_at}")
    print()
    
    # Check if image file exists
    image_path = test_image.image.path
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return
    
    print(f"📁 Image path: {image_path}")
    print("🔄 Running Gemini analysis...")
    
    # Run analysis
    result = gemini_service.analyze_image(image_path, test_image)
    
    print("\n📋 Analysis Result:")
    print(f"   Success: {result['success']}")
    
    if result['success']:
        print(f"   Description: {result['description'][:200]}...")
        print(f"   Model: {result.get('model_info', {}).get('model_name', 'Unknown')}")
        print(f"   Timestamp: {result['timestamp']}")
        
        if result.get('demo_mode'):
            print("   🎭 Running in DEMO mode")
        else:
            print("   🤖 Using real Gemini API")
    else:
        print(f"   Error: {result.get('error', 'Unknown error')}")
    
    print("\n✅ Test completed!")

def test_database_integration():
    """Test database integration with Gemini fields"""
    print("\n🗄️  Testing Database Integration")
    print("=" * 50)
    
    # Check recent prediction results
    recent_results = PredictionResult.objects.all().order_by('-id')[:3]
    
    if not recent_results.exists():
        print("⚠️  No prediction results found in database.")
        return
    
    for result in recent_results:
        print(f"\n📊 Prediction Result ID: {result.id}")
        print(f"   Image: {result.image.original_filename}")
        print(f"   Prediction: {result.prediction}")
        print(f"   Gemini Status: {result.gemini_analysis_status}")
        
        if result.image_description:
            print(f"   Description: {result.image_description[:100]}...")
        else:
            print("   Description: Not available")
        
        if result.gemini_processed_at:
            print(f"   Processed: {result.gemini_processed_at}")

if __name__ == "__main__":
    try:
        test_gemini_service()
        test_database_integration()
        
        print("\n🎉 All tests completed successfully!")
        print("\n💡 Next steps:")
        print("   1. Open http://127.0.0.1:8000/ in your browser")
        print("   2. Upload a medical image")
        print("   3. View the results with both cancer prediction and image description")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
