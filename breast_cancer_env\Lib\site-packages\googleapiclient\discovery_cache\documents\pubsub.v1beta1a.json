{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/pubsub": {"description": "View and manage Pub/Sub topics and subscriptions"}}}}, "basePath": "", "baseUrl": "https://pubsub.googleapis.com/", "batchPath": "batch", "canonicalName": "Pubsub", "description": "Provides reliable, many-to-many, asynchronous messaging between applications. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/pubsub/docs", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://pubsub.me-central2.rep.googleapis.com/", "location": "me-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.europe-west3.rep.googleapis.com/", "location": "europe-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.europe-west8.rep.googleapis.com/", "location": "europe-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.europe-west9.rep.googleapis.com/", "location": "europe-west9"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-central1.rep.googleapis.com/", "location": "us-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-central2.rep.googleapis.com/", "location": "us-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-east1.rep.googleapis.com/", "location": "us-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-east4.rep.googleapis.com/", "location": "us-east4"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-east5.rep.googleapis.com/", "location": "us-east5"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-south1.rep.googleapis.com/", "location": "us-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-west1.rep.googleapis.com/", "location": "us-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-west2.rep.googleapis.com/", "location": "us-west2"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-west3.rep.googleapis.com/", "location": "us-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-west4.rep.googleapis.com/", "location": "us-west4"}], "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "pubsub:v1beta1a", "kind": "discovery#restDescription", "mtlsRootUrl": "https://pubsub.mtls.googleapis.com/", "name": "pubsub", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"subscriptions": {"methods": {"acknowledge": {"description": "Acknowledges a particular received message: the Pub/Sub system can remove the given message from the subscription. Acknowledging a message whose Ack deadline has expired may succeed, but the message could have been already redelivered. Acknowledging a message more than once will not result in an error. This is only used for messages received via pull.", "flatPath": "v1beta1a/subscriptions/acknowledge", "httpMethod": "POST", "id": "pubsub.subscriptions.acknowledge", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/subscriptions/acknowledge", "request": {"$ref": "AcknowledgeRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "create": {"description": "Creates a subscription on a given topic for a given subscriber. If the subscription already exists, returns ALREADY_EXISTS. If the corresponding topic doesn't exist, returns NOT_FOUND. If the name is not provided in the request, the server will assign a random name for this subscription on the same project as the topic.", "flatPath": "v1beta1a/subscriptions", "httpMethod": "POST", "id": "pubsub.subscriptions.create", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/subscriptions", "request": {"$ref": "Subscription"}, "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "delete": {"description": "Deletes an existing subscription. All pending messages in the subscription are immediately dropped. Calls to Pull after deletion will return NOT_FOUND.", "flatPath": "v1beta1a/subscriptions/{subscriptionsId}", "httpMethod": "DELETE", "id": "pubsub.subscriptions.delete", "parameterOrder": ["subscription"], "parameters": {"subscription": {"description": "The subscription to delete.", "location": "path", "pattern": "^.*$", "required": true, "type": "string"}}, "path": "v1beta1a/subscriptions/{+subscription}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "get": {"description": "Gets the configuration details of a subscription.", "flatPath": "v1beta1a/subscriptions/{subscriptionsId}", "httpMethod": "GET", "id": "pubsub.subscriptions.get", "parameterOrder": ["subscription"], "parameters": {"subscription": {"description": "The name of the subscription to get.", "location": "path", "pattern": "^.*$", "required": true, "type": "string"}}, "path": "v1beta1a/subscriptions/{+subscription}", "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "list": {"description": "Lists matching subscriptions.", "flatPath": "v1beta1a/subscriptions", "httpMethod": "GET", "id": "pubsub.subscriptions.list", "parameterOrder": [], "parameters": {"maxResults": {"description": "Maximum number of subscriptions to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value obtained in the last ListSubscriptionsResponse for continuation.", "location": "query", "type": "string"}, "query": {"description": "A valid label query expression.", "location": "query", "type": "string"}}, "path": "v1beta1a/subscriptions", "response": {"$ref": "ListSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "modifyAckDeadline": {"description": "Modifies the Ack deadline for a message received from a pull request.", "flatPath": "v1beta1a/subscriptions/modifyAckDeadline", "httpMethod": "POST", "id": "pubsub.subscriptions.modifyAckDeadline", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/subscriptions/modifyAckDeadline", "request": {"$ref": "ModifyAckDeadlineRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "modifyPushConfig": {"description": "Modifies the PushConfig for a specified subscription. This method can be used to suspend the flow of messages to an endpoint by clearing the PushConfig field in the request. Messages will be accumulated for delivery even if no push configuration is defined or while the configuration is modified.", "flatPath": "v1beta1a/subscriptions/modifyPushConfig", "httpMethod": "POST", "id": "pubsub.subscriptions.modifyPushConfig", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/subscriptions/modifyPushConfig", "request": {"$ref": "ModifyPushConfigRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "pull": {"description": "Pulls a single message from the server. If return_immediately is true, and no messages are available in the subscription, this method returns FAILED_PRECONDITION. The system is free to return an UNAVAILABLE error if no messages are available in a reasonable amount of time (to reduce system load).", "flatPath": "v1beta1a/subscriptions/pull", "httpMethod": "POST", "id": "pubsub.subscriptions.pull", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/subscriptions/pull", "request": {"$ref": "PullRequest"}, "response": {"$ref": "PullResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "pullBatch": {"description": "Pulls messages from the server. Returns an empty list if there are no messages available in the backlog. The system is free to return UNAVAILABLE if there are too many pull requests outstanding for the given subscription.", "flatPath": "v1beta1a/subscriptions/pullBatch", "httpMethod": "POST", "id": "pubsub.subscriptions.pullBatch", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/subscriptions/pullBatch", "request": {"$ref": "PullBatchRequest"}, "response": {"$ref": "PullBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}}}, "topics": {"methods": {"create": {"description": "Creates the given topic with the given name.", "flatPath": "v1beta1a/topics", "httpMethod": "POST", "id": "pubsub.topics.create", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/topics", "request": {"$ref": "Topic"}, "response": {"$ref": "Topic"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "delete": {"description": "Deletes the topic with the given name. Returns NOT_FOUND if the topic does not exist. After a topic is deleted, a new topic may be created with the same name.", "flatPath": "v1beta1a/topics/{topicsId}", "httpMethod": "DELETE", "id": "pubsub.topics.delete", "parameterOrder": ["topic"], "parameters": {"topic": {"description": "Name of the topic to delete.", "location": "path", "pattern": "^.*$", "required": true, "type": "string"}}, "path": "v1beta1a/topics/{+topic}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "get": {"description": "Gets the configuration of a topic. Since the topic only has the name attribute, this method is only useful to check the existence of a topic. If other attributes are added in the future, they will be returned here.", "flatPath": "v1beta1a/topics/{topicsId}", "httpMethod": "GET", "id": "pubsub.topics.get", "parameterOrder": ["topic"], "parameters": {"topic": {"description": "The name of the topic to get.", "location": "path", "pattern": "^.*$", "required": true, "type": "string"}}, "path": "v1beta1a/topics/{+topic}", "response": {"$ref": "Topic"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "list": {"description": "Lists matching topics.", "flatPath": "v1beta1a/topics", "httpMethod": "GET", "id": "pubsub.topics.list", "parameterOrder": [], "parameters": {"maxResults": {"description": "Maximum number of topics to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value obtained in the last ListTopicsResponse for continuation.", "location": "query", "type": "string"}, "query": {"description": "A valid label query expression.", "location": "query", "type": "string"}}, "path": "v1beta1a/topics", "response": {"$ref": "ListTopicsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "publish": {"description": "Adds a message to the topic. Returns NOT_FOUND if the topic does not exist.", "flatPath": "v1beta1a/topics/publish", "httpMethod": "POST", "id": "pubsub.topics.publish", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/topics/publish", "request": {"$ref": "PublishRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "publishBatch": {"description": "Adds one or more messages to the topic. Returns NOT_FOUND if the topic does not exist.", "flatPath": "v1beta1a/topics/publishBatch", "httpMethod": "POST", "id": "pubsub.topics.publishBatch", "parameterOrder": [], "parameters": {}, "path": "v1beta1a/topics/publishBatch", "request": {"$ref": "PublishBatchRequest"}, "response": {"$ref": "PublishBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}}}}, "revision": "20240730", "rootUrl": "https://pubsub.googleapis.com/", "schemas": {"AcknowledgeRequest": {"description": "Request for the Acknowledge method.", "id": "AcknowledgeRequest", "properties": {"ackId": {"description": "The acknowledgment ID for the message being acknowledged. This was returned by the Pub/Sub system in the Pull response.", "items": {"type": "string"}, "type": "array"}, "subscription": {"description": "The subscription whose message is being acknowledged.", "type": "string"}}, "type": "object"}, "Empty": {"description": "An empty message that you can re-use to avoid defining duplicated empty messages in your project. A typical example is to use it as argument or the return value of a service API. For instance: service Foo { rpc Bar (proto2.Empty) returns (proto2.Empty) { }; }; BEGIN GOOGLE-INTERNAL The difference between this one and net/rpc/empty-message.proto is that 1) The generated message here is in proto2 C++ API. 2) The proto2.Empty has minimum dependencies (no message_set or net/rpc dependencies) END GOOGLE-INTERNAL", "id": "Empty", "properties": {}, "type": "object"}, "Label": {"description": "A key-value pair applied to a given object.", "id": "Label", "properties": {"key": {"description": "The key of a label is a syntactically valid URL (as per RFC 1738) with the \"scheme\" and initial slashes omitted and with the additional restrictions noted below. Each key should be globally unique. The \"host\" portion is called the \"namespace\" and is not necessarily resolvable to a network endpoint. Instead, the namespace indicates what system or entity defines the semantics of the label. Namespaces do not restrict the set of objects to which a label may be associated. Keys are defined by the following grammar: key = hostname \"/\" kpath kpath = ksegment *[ \"/\" ksegment ] ksegment = alphadigit | *[ alphadigit | \"-\" | \"_\" | \".\" ] where \"hostname\" and \"alphadigit\" are defined as in RFC 1738. Example key: spanner.google.com/universe", "type": "string"}, "numValue": {"description": "An integer value.", "format": "int64", "type": "string"}, "strValue": {"description": "A string value.", "type": "string"}}, "type": "object"}, "ListSubscriptionsResponse": {"description": "Response for the ListSubscriptions method.", "id": "ListSubscriptionsResponse", "properties": {"nextPageToken": {"description": "If not empty, indicates that there are more subscriptions that match the request and this value should be passed to the next ListSubscriptionsRequest to continue.", "type": "string"}, "subscription": {"description": "The subscriptions that match the request.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "ListTopicsResponse": {"description": "Response for the ListTopics method.", "id": "ListTopicsResponse", "properties": {"nextPageToken": {"description": "If not empty, indicates that there are more topics that match the request, and this value should be passed to the next ListTopicsRequest to continue.", "type": "string"}, "topic": {"description": "The resulting topics.", "items": {"$ref": "Topic"}, "type": "array"}}, "type": "object"}, "ModifyAckDeadlineRequest": {"description": "Request for the ModifyAckDeadline method.", "id": "ModifyAckDeadlineRequest", "properties": {"ackDeadlineSeconds": {"description": "The new ack deadline with respect to the time this request was sent to the Pub/Sub system. Must be >= 0. For example, if the value is 10, the new ack deadline will expire 10 seconds after the ModifyAckDeadline call was made. Specifying zero may immediately make the message available for another pull request.", "format": "int32", "type": "integer"}, "ackId": {"deprecated": true, "description": "The acknowledgment ID. Either this or ack_ids must be populated, not both.", "type": "string"}, "ackIds": {"description": "List of acknowledgment IDs. Either this field or ack_id should be populated, not both.", "items": {"type": "string"}, "type": "array"}, "subscription": {"description": "Next Index: 5 The name of the subscription from which messages are being pulled.", "type": "string"}}, "type": "object"}, "ModifyPushConfigRequest": {"description": "Request for the ModifyPushConfig method.", "id": "ModifyPushConfigRequest", "properties": {"pushConfig": {"$ref": "PushConfig", "description": "An empty push_config indicates that the Pub/Sub system should pause pushing messages from the given subscription."}, "subscription": {"description": "The name of the subscription.", "type": "string"}}, "type": "object"}, "PublishBatchRequest": {"description": "Request for the PublishBatch method.", "id": "PublishBatchRequest", "properties": {"messages": {"description": "The messages to publish.", "items": {"$ref": "PubsubMessage"}, "type": "array"}, "topic": {"description": "The messages in the request will be published on this topic.", "type": "string"}}, "type": "object"}, "PublishBatchResponse": {"description": "Response for the PublishBatch method.", "id": "PublishBatchResponse", "properties": {"messageIds": {"description": "The server-assigned ID of each published message, in the same order as the messages in the request. IDs are guaranteed to be unique within the topic.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PublishRequest": {"description": "Request for the Publish method.", "id": "PublishRequest", "properties": {"message": {"$ref": "PubsubMessage", "description": "The message to publish."}, "topic": {"description": "The message in the request will be published on this topic.", "type": "string"}}, "type": "object"}, "PubsubEvent": {"description": "An event indicating a received message or truncation event.", "id": "PubsubEvent", "properties": {"deleted": {"description": "Indicates that this subscription has been deleted. (Note that pull subscribers will always receive NOT_FOUND in response in their pull request on the subscription, rather than seeing this boolean.)", "type": "boolean"}, "message": {"$ref": "PubsubMessage", "description": "A received message."}, "subscription": {"description": "The subscription that received the event.", "type": "string"}, "truncated": {"description": "Indicates that this subscription has been truncated.", "type": "boolean"}}, "type": "object"}, "PubsubMessage": {"description": "A message data and its labels.", "id": "PubsubMessage", "properties": {"data": {"description": "The message payload.", "format": "byte", "type": "string"}, "label": {"description": "Optional list of labels for this message. Keys in this collection must be unique.", "items": {"$ref": "Label"}, "type": "array"}, "messageId": {"description": "ID of this message assigned by the server at publication time. Guaranteed to be unique within the topic. This value may be read by a subscriber that receives a PubsubMessage via a Pull call or a push delivery. It must not be populated by a publisher in a Publish call.", "type": "string"}, "publishTime": {"description": "The time at which the message was published. The time is milliseconds since the UNIX epoch.", "format": "int64", "type": "string"}}, "type": "object"}, "PullBatchRequest": {"description": "Request for the PullBatch method.", "id": "PullBatchRequest", "properties": {"maxEvents": {"description": "The maximum number of PubsubEvents returned for this request. The Pub/Sub system may return fewer than the number of events specified.", "format": "int32", "type": "integer"}, "returnImmediately": {"description": "If this is specified as true the system will respond immediately even if it is not able to return a message in the Pull response. Otherwise the system is allowed to wait until at least one message is available rather than returning no messages. The client may cancel the request if it does not wish to wait any longer for the response.", "type": "boolean"}, "subscription": {"description": "The subscription from which messages should be pulled.", "type": "string"}}, "type": "object"}, "PullBatchResponse": {"description": "Response for the PullBatch method.", "id": "PullBatchResponse", "properties": {"pullResponses": {"description": "Received Pub/Sub messages or status events. The Pub/Sub system will return zero messages if there are no more messages available in the backlog. The Pub/Sub system may return fewer than the max_events requested even if there are more messages available in the backlog.", "items": {"$ref": "PullResponse"}, "type": "array"}}, "type": "object"}, "PullRequest": {"description": "Request for the Pull method.", "id": "PullRequest", "properties": {"returnImmediately": {"description": "If this is specified as true the system will respond immediately even if it is not able to return a message in the Pull response. Otherwise the system is allowed to wait until at least one message is available rather than returning FAILED_PRECONDITION. The client may cancel the request if it does not wish to wait any longer for the response.", "type": "boolean"}, "subscription": {"description": "The subscription from which a message should be pulled.", "type": "string"}}, "type": "object"}, "PullResponse": {"description": "Either a PubsubMessage or a truncation event. One of these two must be populated.", "id": "PullResponse", "properties": {"ackId": {"description": "This ID must be used to acknowledge the received event or message.", "type": "string"}, "pubsubEvent": {"$ref": "PubsubEvent", "description": "A pubsub message or truncation event."}}, "type": "object"}, "PushConfig": {"description": "Configuration for a push delivery endpoint.", "id": "PushConfig", "properties": {"pushEndpoint": {"description": "A URL locating the endpoint to which messages should be pushed. For example, a Webhook endpoint might use \"https://example.com/push\".", "type": "string"}}, "type": "object"}, "Subscription": {"description": "A subscription resource.", "id": "Subscription", "properties": {"ackDeadlineSeconds": {"description": "For either push or pull delivery, the value is the maximum time after a subscriber receives a message before the subscriber should acknowledge or Nack the message. If the Ack deadline for a message passes without an Ack or a Nack, the Pub/Sub system will eventually redeliver the message. If a subscriber acknowledges after the deadline, the Pub/Sub system may accept the Ack, but it is possible that the message has been already delivered again. Multiple Acks to the message are allowed and will succeed. For push delivery, this value is used to set the request timeout for the call to the push endpoint. For pull delivery, this value is used as the initial value for the Ack deadline. It may be overridden for each message using its corresponding ack_id with ModifyAckDeadline. While a message is outstanding (i.e. it has been delivered to a pull subscriber and the subscriber has not yet Acked or Nacked), the Pub/Sub system will not deliver that message to another pull subscriber (on a best-effort basis).", "format": "int32", "type": "integer"}, "name": {"description": "Name of the subscription.", "type": "string"}, "pushConfig": {"$ref": "PushConfig", "description": "If push delivery is used with this subscription, this field is used to configure it."}, "topic": {"description": "The name of the topic from which this subscription is receiving messages.", "type": "string"}}, "type": "object"}, "Topic": {"description": "A topic resource.", "id": "Topic", "properties": {"name": {"description": "Name of the topic.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Pub/Sub API", "version": "v1beta1a"}