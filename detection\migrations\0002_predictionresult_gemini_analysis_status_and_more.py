# Generated by Django 5.2.4 on 2025-07-16 07:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('detection', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='predictionresult',
            name='gemini_analysis_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='predictionresult',
            name='gemini_processed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='predictionresult',
            name='image_description',
            field=models.TextField(blank=True, help_text='AI-generated description of the image', null=True),
        ),
    ]
