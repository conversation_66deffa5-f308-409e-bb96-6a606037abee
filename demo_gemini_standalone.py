#!/usr/bin/env python
"""
Standalone demo of Gemini image analysis using your provided code pattern
This demonstrates the exact integration you requested
"""

import google.generativeai as genai
import os
from pathlib import Path

# Configure API key (using the same key you provided)
genai.configure(api_key='AIzaSyBqZnGb2eZi2p_t4cB7Kz0eEzY3fpsJENA')

def analyze_image_with_gemini(image_path, custom_prompt=None):
    """
    Analyze an image using Gemini API - based on your provided code pattern
    """
    print(f"🔍 Analyzing image: {image_path}")
    
    # Check if image exists
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return None
    
    try:
        # Load image (your code pattern)
        with open(image_path, "rb") as f:
            image_bytes = f.read()
        
        # Initialize model (your code pattern)
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Default prompt for medical images
        if custom_prompt is None:
            custom_prompt = """
            Please provide a detailed medical description of this image. Focus on:
            1. Visual characteristics (image quality, clarity, color patterns, texture)
            2. Technical aspects (resolution, lighting, contrast)
            3. General observations (overall appearance, distinctive features)
            
            Provide a professional, objective description suitable for medical documentation.
            Do not attempt medical diagnosis - focus on describing what is visually observable.
            """
        
        # Send image and prompt (your code pattern adapted)
        response = model.generate_content([
            {"mime_type": "image/png", "data": image_bytes},  # Adjusted for PNG
            custom_prompt
        ])
        
        # Print the result (your code pattern)
        print("📋 Gemini Analysis Result:")
        print("=" * 50)
        print(response.text)
        print("=" * 50)
        
        return response.text
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        return None

def demo_with_existing_images():
    """
    Demo using existing images in the media/uploads directory
    """
    print("🎯 Gemini Image Analysis Demo")
    print("Using your provided API key and code pattern")
    print("=" * 60)
    
    # Look for images in the uploads directory
    uploads_dir = Path("media/uploads")
    
    if not uploads_dir.exists():
        print(f"❌ Uploads directory not found: {uploads_dir}")
        return
    
    # Find image files
    image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(uploads_dir.glob(f"*{ext}"))
        image_files.extend(uploads_dir.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print("❌ No image files found in uploads directory")
        return
    
    print(f"📁 Found {len(image_files)} image(s)")
    
    # Analyze the first few images
    for i, image_path in enumerate(image_files[:3]):  # Limit to first 3 images
        print(f"\n🖼️  Image {i+1}: {image_path.name}")
        
        # Use your exact code pattern
        result = analyze_image_with_gemini(str(image_path))
        
        if result:
            print("✅ Analysis completed successfully!")
        else:
            print("❌ Analysis failed!")
        
        print("\n" + "-" * 60)

def demo_with_simple_caption():
    """
    Demo using your exact original code pattern for simple captioning
    """
    print("\n🎯 Simple Caption Demo (Your Original Code Pattern)")
    print("=" * 60)
    
    # Look for the papiloma.png file you mentioned
    image_path = "media/uploads/papiloma.png"
    
    # Check for any papiloma file
    uploads_dir = Path("media/uploads")
    papiloma_files = list(uploads_dir.glob("papiloma*"))
    
    if papiloma_files:
        image_path = str(papiloma_files[0])
    elif not os.path.exists(image_path):
        print(f"❌ papiloma.png not found. Looking for any image...")
        # Use any available image
        image_files = list(uploads_dir.glob("*.png")) + list(uploads_dir.glob("*.jpg"))
        if image_files:
            image_path = str(image_files[0])
        else:
            print("❌ No images found for demo")
            return
    
    print(f"📸 Using image: {image_path}")
    
    # Your exact code pattern with simple caption prompt
    try:
        # Load image 
        with open(image_path, "rb") as f: 
            image_bytes = f.read() 
        
        # Initialize model 
        model = genai.GenerativeModel('gemini-1.5-flash') 
        
        # Send image and prompt 
        response = model.generate_content( 
            [ 
                {"mime_type": "image/jpeg", "data": image_bytes}, 
                "Caption this image." 
            ] 
        ) 
        
        # Print the result 
        print("📝 Simple Caption Result:")
        print(response.text)
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    try:
        # Run both demos
        demo_with_existing_images()
        demo_with_simple_caption()
        
        print("\n🎉 Demo completed!")
        print("\n💡 Integration Summary:")
        print("✅ Your Gemini API key is working")
        print("✅ Image analysis is functional")
        print("✅ Both simple captions and detailed medical descriptions work")
        print("✅ Integration is now active in your Django application")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
