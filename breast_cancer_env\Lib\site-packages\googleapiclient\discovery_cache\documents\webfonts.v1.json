{"basePath": "", "baseUrl": "https://webfonts.googleapis.com/", "batchPath": "batch", "canonicalName": "Webfonts", "description": "The Google Web Fonts Developer API lets you retrieve information about web fonts served by Google.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/fonts/docs/developer_api", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "webfonts:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://webfonts.mtls.googleapis.com/", "name": "webfonts", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"webfonts": {"methods": {"list": {"description": "Retrieves the list of fonts currently served by the Google Fonts Developer API.", "flatPath": "v1/webfonts", "httpMethod": "GET", "id": "webfonts.webfonts.list", "parameterOrder": [], "parameters": {"capability": {"description": "Controls the font urls in `Webfont.files`, by default, static ttf fonts are sent.", "enum": ["CAPABILITY_UNSPECIFIED", "WOFF2", "VF"], "enumDescriptions": ["Default means static ttf fonts.", "Use WOFF2(Compressed)instead of ttf.", "Prefer variable font files instead of static fonts instantiated at standard weights."], "location": "query", "repeated": true, "type": "string"}, "category": {"description": "Filters by Webfont.category, if category is found in Webfont.categories. If not set, returns all families.", "location": "query", "type": "string"}, "family": {"description": "Filters by Webfont.family, using literal match. If not set, returns all families", "location": "query", "repeated": true, "type": "string"}, "sort": {"description": "Enables sorting of the list.", "enum": ["SORT_UNDEFINED", "ALPHA", "DATE", "POPULARITY", "STYLE", "TRENDING"], "enumDescriptions": ["No sorting specified, use the default sorting method.", "Sort alphabetically", "Sort by date added", "Sort by popularity", "Sort by number of styles", "Sort by trending"], "location": "query", "type": "string"}, "subset": {"description": "Filters by Webfont.subset, if subset is found in Webfont.subsets. If not set, returns all families.", "location": "query", "type": "string"}}, "path": "v1/webfonts", "response": {"$ref": "WebfontList"}}}}}, "revision": "20250121", "rootUrl": "https://webfonts.googleapis.com/", "schemas": {"Axis": {"description": "Metadata for a variable font axis.", "id": "Axis", "properties": {"end": {"description": "maximum value", "format": "float", "type": "number"}, "start": {"description": "minimum value", "format": "float", "type": "number"}, "tag": {"description": "tag name.", "type": "string"}}, "type": "object"}, "Webfont": {"description": "Metadata describing a family of fonts.", "id": "Webfont", "properties": {"axes": {"description": "Axis for variable fonts.", "items": {"$ref": "Axis"}, "type": "array"}, "category": {"description": "The category of the font.", "type": "string"}, "colorCapabilities": {"description": "The color format(s) available for this family.", "items": {"type": "string"}, "type": "array"}, "family": {"description": "The name of the font.", "type": "string"}, "files": {"additionalProperties": {"type": "string"}, "description": "The font files (with all supported scripts) for each one of the available variants, as a key : value map.", "type": "object"}, "kind": {"description": "This kind represents a webfont object in the webfonts service.", "type": "string"}, "lastModified": {"description": "The date (format \"yyyy-MM-dd\") the font was modified for the last time.", "type": "string"}, "menu": {"description": "Font URL for menu subset, a subset of the font that is enough to display the font name", "type": "string"}, "subsets": {"description": "The scripts supported by the font.", "items": {"type": "string"}, "type": "array"}, "variants": {"description": "The available variants for the font.", "items": {"type": "string"}, "type": "array"}, "version": {"description": "The font version.", "type": "string"}}, "type": "object"}, "WebfontList": {"description": "Response containing the list of fonts currently served by the Google Fonts API.", "id": "WebfontList", "properties": {"items": {"description": "The list of fonts currently served by the Google Fonts API.", "items": {"$ref": "Webfont"}, "type": "array"}, "kind": {"description": "This kind represents a list of webfont objects in the webfonts service.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Web Fonts Developer API", "version": "v1"}