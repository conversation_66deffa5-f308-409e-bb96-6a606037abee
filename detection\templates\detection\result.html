{% extends 'detection/base.html' %}

{% block title %}Analysis Result - Breast Cancer Detection{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-image me-2"></i>Uploaded Image</h3>
                </div>
                <div class="card-body text-center">
                    <img src="{{ image.image.url }}" class="img-fluid rounded" alt="Uploaded medical image">
                    <div class="mt-3">
                        <p class="mb-1"><strong>Filename:</strong> {{ image.original_filename }}</p>
                        <p class="mb-0"><small class="text-muted">Uploaded: {{ image.uploaded_at|date:"M d, Y H:i" }}</small></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-bar me-2"></i>Analysis Results</h3>
                </div>
                <div class="card-body">
                    {% if result.prediction == 'pending' %}
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Processing...</span>
                            </div>
                            <h4>Processing Image...</h4>
                            <p class="text-muted">Please wait while our AI analyzes your image.</p>
                            <script>
                                // Auto-refresh for pending results
                                setTimeout(function() {
                                    location.reload();
                                }, 3000);
                            </script>
                        </div>
                    {% elif result.prediction == 'error' %}
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                            <h4 class="text-warning">Processing Error</h4>
                            <p class="text-muted">There was an error processing your image. Please try again.</p>
                            {% if ml_data.error %}
                                <div class="alert alert-warning">
                                    <strong>Error Details:</strong> {{ ml_data.error }}
                                </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="result-summary mb-4">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center p-3 rounded {% if result.prediction == 'benign' %}bg-success bg-opacity-10{% else %}bg-danger bg-opacity-10{% endif %}">
                                        <i class="fas {% if result.prediction == 'benign' %}fa-check-circle text-success{% else %}fa-exclamation-triangle text-danger{% endif %} fa-3x mb-2"></i>
                                        <h4 class="{% if result.prediction == 'benign' %}text-success{% else %}text-danger{% endif %}">
                                            {{ result.get_prediction_display }}
                                        </h4>
                                        {% if ml_data and ml_data.analysis %}
                                            <span class="badge bg-{% if ml_data.analysis.risk_level == 'LOW' %}success{% elif ml_data.analysis.risk_level == 'MODERATE' %}warning{% else %}danger{% endif %}">
                                                {{ ml_data.analysis.risk_level }} RISK
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-3">
                                        <h5>Confidence Level</h5>
                                        <div class="progress mb-2" style="height: 20px;">
                                            <div class="progress-bar {% if result.prediction == 'benign' %}bg-success{% else %}bg-danger{% endif %}"
                                                 role="progressbar"
                                                 style="width: {% if result.confidence %}{{ result.confidence|floatformat:0 }}{% else %}0{% endif %}%">
                                                {% if result.confidence %}{{ result.confidence|floatformat:1 }}%{% else %}0%{% endif %}
                                            </div>
                                        </div>
                                        {% if ml_data and ml_data.analysis %}
                                            <small class="text-muted">{{ ml_data.analysis.confidence_interpretation }}</small>
                                        {% else %}
                                            <small class="text-muted">Model Confidence</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if ml_data and ml_data.confidence_scores %}
                            <div class="confidence-breakdown mb-4">
                                <h6><i class="fas fa-chart-bar me-2"></i>Detailed Confidence Scores:</h6>
                                {% for class_name, score in ml_data.confidence_scores.items %}
                                    <div class="mb-2">
                                        <div class="d-flex justify-content-between">
                                            <span>{{ class_name|capfirst }}</span>
                                            <span>{{ score|floatformat:1 }}%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar {% if class_name == 'benign' %}bg-success{% else %}bg-danger{% endif %}"
                                                 style="width: {{ score }}%"></div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="alert {% if result.prediction == 'benign' %}alert-success{% else %}alert-danger{% endif %}">
                            <h6><i class="fas fa-info-circle me-2"></i>AI Analysis Interpretation:</h6>
                            {% if ml_data and ml_data.analysis and ml_data.analysis.recommendation %}
                                <p class="mb-0">{{ ml_data.analysis.recommendation }}</p>
                            {% else %}
                                {% if result.prediction == 'benign' %}
                                    <p class="mb-0">The analysis suggests that the tissue appears normal with no obvious signs of malignancy detected.</p>
                                {% else %}
                                    <p class="mb-0">The analysis has detected patterns that may indicate potential abnormalities requiring further medical evaluation.</p>
                                {% endif %}
                            {% endif %}
                        </div>

                        <div class="analysis-details">
                            <h6>Analysis Details:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-calendar me-2"></i><strong>Processed:</strong> {{ result.processed_at|date:"M d, Y H:i:s" }}</li>
                                <li><i class="fas fa-microchip me-2"></i><strong>Model:</strong> CNN-based Classification (Phase 1 - Placeholder)</li>
                                <li><i class="fas fa-cogs me-2"></i><strong>Algorithm:</strong> Deep Learning Neural Network</li>
                            </ul>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Gemini Image Description Section -->
            {% if result.prediction != 'pending' %}
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-eye me-2"></i>AI Image Description</h5>
                        {% if gemini_service_status.gemini_available %}
                            <small class="text-success"><i class="fas fa-check-circle me-1"></i>Powered by Google Gemini</small>
                        {% else %}
                            <small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Demo Mode</small>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if result.gemini_analysis_status == 'pending' %}
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Analyzing image...</span>
                                </div>
                                <p class="text-muted mb-0">Generating image description...</p>
                                <script>
                                    // Auto-refresh for pending Gemini analysis
                                    setTimeout(function() {
                                        location.reload();
                                    }, 2000);
                                </script>
                            </div>
                        {% elif result.gemini_analysis_status == 'completed' and gemini_data.success %}
                            <div class="alert alert-info">
                                <h6><i class="fas fa-robot me-2"></i>AI Visual Analysis:</h6>
                                <p class="mb-2">{{ gemini_data.description }}</p>
                                {% if gemini_data.timestamp %}
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>Generated: {{ result.gemini_processed_at|date:"M d, Y H:i:s" }}
                                    </small>
                                {% endif %}
                            </div>
                        {% elif result.gemini_analysis_status == 'failed' %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Image Description Unavailable:</strong>
                                {% if gemini_data.error %}
                                    {{ gemini_data.error }}
                                {% else %}
                                    Unable to generate image description at this time.
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="alert alert-secondary">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Image Description:</strong> Analysis in progress...
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Medical Disclaimer</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning mb-0">
                            <strong>Important:</strong> This AI analysis is for educational and research purposes only.
                            It should never replace professional medical diagnosis or consultation with qualified healthcare providers.
                            Please consult with a medical professional for proper diagnosis and treatment.
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{% url 'detection:upload_image' %}" class="btn btn-primary me-2">
                <i class="fas fa-upload me-1"></i>Analyze Another Image
            </a>
            <a href="{% url 'detection:home' %}" class="btn btn-secondary">
                <i class="fas fa-home me-1"></i>Back to Home
            </a>
        </div>
    </div>
</div>

{% if result.prediction == 'pending' %}
<script>
// Auto-refresh for pending results (Phase 1 simulation)
setTimeout(function() {
    location.reload();
}, 3000);
</script>
{% endif %}
{% endblock %}
