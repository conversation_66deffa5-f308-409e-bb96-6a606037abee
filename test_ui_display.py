#!/usr/bin/env python
"""
Test script to verify UI display of Gemini integration
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
project_dir = Path(__file__).parent
sys.path.append(str(project_dir))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'breast_cancer_detection.settings')
django.setup()

from detection.models import PredictionResult

def check_recent_results():
    """Check recent results to see what users will see"""
    print("🖥️  UI Display Test - What You Should See")
    print("=" * 60)
    
    recent_results = PredictionResult.objects.all().order_by('-id')[:3]
    
    if not recent_results.exists():
        print("⚠️  No results found. Upload an image first!")
        return
    
    for i, result in enumerate(recent_results, 1):
        print(f"\n📊 Result {i} - {result.image.original_filename}")
        print("-" * 40)
        
        # Cancer Prediction Section
        print("🔬 CANCER PREDICTION:")
        print(f"   Result: {result.get_prediction_display()}")
        if result.confidence:
            print(f"   Confidence: {result.confidence:.1%}")
        print(f"   Processed: {result.processed_at}")
        
        # Gemini Description Section  
        print("\n🤖 AI IMAGE DESCRIPTION:")
        print(f"   Status: {result.gemini_analysis_status}")
        
        if result.gemini_analysis_status == 'completed' and result.image_description:
            # Show first 200 characters of description
            description = result.image_description
            if len(description) > 200:
                description = description[:200] + "..."
            print(f"   Description: {description}")
            print(f"   Generated: {result.gemini_processed_at}")
        elif result.gemini_analysis_status == 'pending':
            print("   Description: ⏳ Generating description...")
        elif result.gemini_analysis_status == 'failed':
            print("   Description: ❌ Failed to generate")
        else:
            print("   Description: 🔄 Processing...")
        
        print("\n" + "="*60)

def show_ui_structure():
    """Show the expected UI structure"""
    print("\n🎨 Expected UI Layout:")
    print("=" * 60)
    print("""
    ┌─────────────────────────────────────────────────────────────┐
    │                    ANALYSIS RESULT PAGE                     │
    ├─────────────────────┬───────────────────────────────────────┤
    │                     │  🔬 ANALYSIS RESULTS                  │
    │  🖼️ UPLOADED IMAGE   │  ┌─────────────────────────────────┐  │
    │                     │  │ Benign/Malignant                │  │
    │  [Image Preview]    │  │ Confidence: XX%                 │  │
    │                     │  │ Risk Level Badge                │  │
    │  Filename: xxx.png  │  └─────────────────────────────────┘  │
    │  Uploaded: Date     │                                       │
    │                     │  📊 Confidence Breakdown              │
    │                     │  [Progress bars for each class]      │
    │                     │                                       │
    │                     │  🤖 AI IMAGE DESCRIPTION              │
    │                     │  ┌─────────────────────────────────┐  │
    │                     │  │ ✅ Powered by Google Gemini     │  │
    │                     │  │                                 │  │
    │                     │  │ [Detailed AI description of    │  │
    │                     │  │  the medical image with        │  │
    │                     │  │  visual characteristics,       │  │
    │                     │  │  technical aspects, and        │  │
    │                     │  │  general observations]         │  │
    │                     │  │                                 │  │
    │                     │  │ Generated: Timestamp            │  │
    │                     │  └─────────────────────────────────┘  │
    │                     │                                       │
    │                     │  ⚠️ MEDICAL DISCLAIMER                │
    │                     │  [Standard medical disclaimer]       │
    └─────────────────────┴───────────────────────────────────────┘
    """)

if __name__ == "__main__":
    check_recent_results()
    show_ui_structure()
    
    print("\n💡 To see this in action:")
    print("1. Go to: http://127.0.0.1:8000/")
    print("2. Click 'Upload Image for Analysis'")
    print("3. Upload any medical image")
    print("4. View the result page with both cancer prediction AND AI description!")
    print("\n✨ The AI Image Description will appear as a new blue info box")
    print("   below the cancer prediction results!")
