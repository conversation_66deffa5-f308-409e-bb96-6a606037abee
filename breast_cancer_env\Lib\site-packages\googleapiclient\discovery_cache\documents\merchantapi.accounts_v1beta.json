{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "", "baseUrl": "https://merchantapi.googleapis.com/", "batchPath": "batch", "canonicalName": "Merchant", "description": "Programmatically manage your Merchant Center Accounts.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/merchant/api", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "merchantapi:accounts_v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://merchantapi.mtls.googleapis.com/", "name": "merchantapi", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"methods": {"createAndConfigure": {"description": "Creates a Merchant Center account with additional configuration. Adds the user that makes the request as an admin for the new account.", "flatPath": "accounts/v1beta/accounts:createAndConfigure", "httpMethod": "POST", "id": "merchantapi.accounts.createAndConfigure", "parameterOrder": [], "parameters": {}, "path": "accounts/v1beta/accounts:createAndConfigure", "request": {"$ref": "CreateAndConfigureAccountRequest"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes the specified account regardless of its type: standalone, advanced account or sub-account. Deleting an advanced account leads to the deletion of all of its sub-accounts. Executing this method requires admin access. The deletion succeeds only if the account does not provide services to any other account and has no processed offers. You can use the `force` parameter to override this.", "flatPath": "accounts/v1beta/accounts/{accountsId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to `true`, the account is deleted even if it provides services to other accounts or has processed offers.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the account to delete. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves an account from your Merchant Center account. After inserting, updating, or deleting an account, it may take several minutes before changes take effect.", "flatPath": "accounts/v1beta/accounts/{accountsId}", "httpMethod": "GET", "id": "merchantapi.accounts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the account to retrieve. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Note: For the `accounts.list` method, quota and limits usage are charged for each user, and not for the Merchant Center ID or the advanced account ID. To list several sub-accounts, you should use the `accounts.listSubaccounts` method, which is more suitable for advanced accounts use case.", "flatPath": "accounts/v1beta/accounts", "httpMethod": "GET", "id": "merchantapi.accounts.list", "parameterOrder": [], "parameters": {"filter": {"description": "Optional. Returns only accounts that match the [filter](https://developers.google.com/merchant/api/guides/accounts/filter). For more details, see the [filter syntax reference](https://developers.google.com/merchant/api/guides/accounts/filter-syntax).", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of accounts to return. The service may return fewer than this value. If unspecified, at most 250 accounts are returned. The maximum value is 500; values above 500 are coerced to 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `accounts.list` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided in the `accounts.list` request must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "accounts/v1beta/accounts", "response": {"$ref": "ListAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "listSubaccounts": {"description": "List all sub-accounts for a given advanced account. This is a convenience wrapper for the more powerful `accounts.list` method. This method will produce the same results as calling `ListsAccounts` with the following filter: `relationship(providerId={parent} AND service(type=\"ACCOUNT_AGGREGATION\"))`", "flatPath": "accounts/v1beta/accounts/{accountsId}:listSubaccounts", "httpMethod": "GET", "id": "merchantapi.accounts.listSubaccounts", "parameterOrder": ["provider"], "parameters": {"pageSize": {"description": "Optional. The maximum number of accounts to return. The service may return fewer than this value. If unspecified, at most 250 accounts are returned. The maximum value is 500; values above 500 are coerced to 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `accounts.list` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided in the `accounts.list` request must match the call that provided the page token.", "location": "query", "type": "string"}, "provider": {"description": "Required. The aggregation service provider. Format: `accounts/{accountId}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+provider}:listSubaccounts", "response": {"$ref": "ListSubAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates an account regardless of its type: standalone, advanced account or sub-account. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}", "httpMethod": "PATCH", "id": "merchantapi.accounts.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the account. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `account_name` - `adult_content` - `language_code` - `time_zone`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "Account"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/content"]}}, "resources": {"autofeedSettings": {"methods": {"getAutofeedSettings": {"description": "Retrieves the autofeed settings of an account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/autofeedSettings", "httpMethod": "GET", "id": "merchantapi.accounts.autofeedSettings.getAutofeedSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the autofeed settings. Format: `accounts/{account}/autofeedSettings`", "location": "path", "pattern": "^accounts/[^/]+/autofeedSettings$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "AutofeedSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateAutofeedSettings": {"description": "Updates the autofeed settings of an account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/autofeedSettings", "httpMethod": "PATCH", "id": "merchantapi.accounts.autofeedSettings.updateAutofeedSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the autofeed settings. Format: `accounts/{account}/autofeedSettings`.", "location": "path", "pattern": "^accounts/[^/]+/autofeedSettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. List of fields being updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "AutofeedSettings"}, "response": {"$ref": "AutofeedSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "automaticImprovements": {"methods": {"getAutomaticImprovements": {"description": "Retrieves the automatic improvements of an account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/automaticImprovements", "httpMethod": "GET", "id": "merchantapi.accounts.automaticImprovements.getAutomaticImprovements", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the automatic improvements. Format: `accounts/{account}/automaticImprovements`", "location": "path", "pattern": "^accounts/[^/]+/automaticImprovements$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "AutomaticImprovements"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateAutomaticImprovements": {"description": "Updates the automatic improvements of an account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/automaticImprovements", "httpMethod": "PATCH", "id": "merchantapi.accounts.automaticImprovements.updateAutomaticImprovements", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the automatic improvements. Format: `accounts/{account}/automaticImprovements`.", "location": "path", "pattern": "^accounts/[^/]+/automaticImprovements$", "required": true, "type": "string"}, "updateMask": {"description": "Required. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `item_updates` - `item_updates.account_level_settings` - `image_improvements` - `image_improvements.account_level_settings` - `shipping_improvements` - `shipping_improvements.allow_shipping_improvements`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "AutomaticImprovements"}, "response": {"$ref": "AutomaticImprovements"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "businessIdentity": {"methods": {"getBusinessIdentity": {"description": "Retrieves the business identity of an account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/businessIdentity", "httpMethod": "GET", "id": "merchantapi.accounts.businessIdentity.getBusinessIdentity", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the business identity. Format: `accounts/{account}/businessIdentity`. For example, `accounts/123456/businessIdentity`.", "location": "path", "pattern": "^accounts/[^/]+/businessIdentity$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "BusinessIdentity"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateBusinessIdentity": {"description": "Updates the business identity of an account. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/businessIdentity", "httpMethod": "PATCH", "id": "merchantapi.accounts.businessIdentity.updateBusinessIdentity", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the business identity. Format: `accounts/{account}/businessIdentity`", "location": "path", "pattern": "^accounts/[^/]+/businessIdentity$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `black_owned` - `latino_owned` - `promotions_consent` - `small_business` - `veteran_owned` - `women_owned`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "BusinessIdentity"}, "response": {"$ref": "BusinessIdentity"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "businessInfo": {"methods": {"getBusinessInfo": {"description": "Retrieves the business info of an account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/businessInfo", "httpMethod": "GET", "id": "merchantapi.accounts.businessInfo.getBusinessInfo", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the business info. Format: `accounts/{account}/businessInfo`. For example, `accounts/123456/businessInfo`.", "location": "path", "pattern": "^accounts/[^/]+/businessInfo$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "BusinessInfo"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateBusinessInfo": {"description": "Updates the business info of an account. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/businessInfo", "httpMethod": "PATCH", "id": "merchantapi.accounts.businessInfo.updateBusinessInfo", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the business info. Format: `accounts/{account}/businessInfo`", "location": "path", "pattern": "^accounts/[^/]+/businessInfo$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `address` - `customer_service` - `korean_business_registration_number`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "BusinessInfo"}, "response": {"$ref": "BusinessInfo"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "emailPreferences": {"methods": {"getEmailPreferences": {"description": "Returns the email preferences for a Merchant Center account user. This service only permits retrieving and updating email preferences for the authenticated user. Use the name=accounts/*/users/me/emailPreferences alias to get preferences for the authenticated user.", "flatPath": "accounts/v1beta/accounts/{accountsId}/users/{usersId}/emailPreferences", "httpMethod": "GET", "id": "merchantapi.accounts.emailPreferences.getEmailPreferences", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `EmailPreferences` resource. Format: `accounts/{account}/users/{email}/emailPreferences`", "location": "path", "pattern": "^accounts/[^/]+/users/[^/]+/emailPreferences$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "EmailPreferences"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateEmailPreferences": {"description": "Updates the email preferences for a Merchant Center account user. Advanced account users should specify the advanced account rather than a sub-account of the advanced account. Preferences which are not explicitly selected in the update mask will not be updated. It is invalid for updates to specify an UNCONFIRMED opt-in status value. Use the name=accounts/*/users/me/emailPreferences alias to update preferences for the authenticated user.", "flatPath": "accounts/v1beta/accounts/{accountsId}/users/{usersId}/emailPreferences", "httpMethod": "PATCH", "id": "merchantapi.accounts.emailPreferences.updateEmailPreferences", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the EmailPreferences. The endpoint is only supported for the authenticated user.", "location": "path", "pattern": "^accounts/[^/]+/users/[^/]+/emailPreferences$", "required": true, "type": "string"}, "updateMask": {"description": "Required. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `news_and_tips`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "EmailPreferences"}, "response": {"$ref": "EmailPreferences"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "gbpAccounts": {"methods": {"linkGbpAccount": {"description": "Link the specified merchant to a GBP account for all countries. To run this method, you must have admin access to the Merchant Center account. If you don't have admin access, the request fails with the error message `User is not an administrator of account {ACCOUNT_ID}`.", "flatPath": "accounts/v1beta/accounts/{accountsId}/gbpAccounts:linkGbpAccount", "httpMethod": "POST", "id": "merchantapi.accounts.gbpAccounts.linkGbpAccount", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the parent resource to which the GBP account is linked. Format: `accounts/{account}`.", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/gbpAccounts:linkGbpAccount", "request": {"$ref": "LinkGbpAccountRequest"}, "response": {"$ref": "LinkGbpAccountResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "List the GBP accounts for a given merchant.", "flatPath": "accounts/v1beta/accounts/{accountsId}/gbpAccounts", "httpMethod": "GET", "id": "merchantapi.accounts.gbpAccounts.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of `GbpAccount` resources to return. The service returns fewer than this value if the number of gbp accounts is less that than the `pageSize`. The default value is 50. The maximum value is 1000; If a value higher than the maximum is specified, then the `pageSize` will default to the maximum.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListGbpAccounts` call. Provide the page token to retrieve the subsequent page. When paginating, all other parameters provided to `ListGbpAccounts` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource under which the GBP accounts are listed. Format: `accounts/{account}`.", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/gbpAccounts", "response": {"$ref": "ListGbpAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "homepage": {"methods": {"claim": {"description": "Claims a store's homepage. Executing this method requires admin access. If the homepage is already claimed, this will recheck the verification (unless the business is exempted from claiming, which also exempts from verification) and return a successful response. If ownership can no longer be verified, it will return an error, but it won't clear the claim. In case of failure, a canonical error message is returned: * PERMISSION_DENIED: User doesn't have the necessary permissions on this Merchant Center account. * FAILED_PRECONDITION: - The account is not a Merchant Center account. - Merchant Center account doesn't have a homepage. - Claiming failed (in this case the error message contains more details).", "flatPath": "accounts/v1beta/accounts/{accountsId}/homepage:claim", "httpMethod": "POST", "id": "merchantapi.accounts.homepage.claim", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the homepage to claim. Format: `accounts/{account}/homepage`", "location": "path", "pattern": "^accounts/[^/]+/homepage$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}:claim", "request": {"$ref": "ClaimHomepageRequest"}, "response": {"$ref": "Homepage"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "getHomepage": {"description": "Retrieves a store's homepage.", "flatPath": "accounts/v1beta/accounts/{accountsId}/homepage", "httpMethod": "GET", "id": "merchantapi.accounts.homepage.getHomepage", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the homepage to retrieve. Format: `accounts/{account}/homepage`", "location": "path", "pattern": "^accounts/[^/]+/homepage$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Homepage"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "unclaim": {"description": "Unclaims a store's homepage. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/homepage:unclaim", "httpMethod": "POST", "id": "merchantapi.accounts.homepage.unclaim", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the homepage to unclaim. Format: `accounts/{account}/homepage`", "location": "path", "pattern": "^accounts/[^/]+/homepage$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}:unclaim", "request": {"$ref": "UnclaimHomepageRequest"}, "response": {"$ref": "Homepage"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateHomepage": {"description": "Updates a store's homepage. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/homepage", "httpMethod": "PATCH", "id": "merchantapi.accounts.homepage.updateHomepage", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the store's homepage. Format: `accounts/{account}/homepage`", "location": "path", "pattern": "^accounts/[^/]+/homepage$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `uri`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "Homepage"}, "response": {"$ref": "Homepage"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "issues": {"methods": {"list": {"description": "Lists all account issues of a Merchant Center account. When called on a multi-client account, this method only returns issues belonging to that account, not its sub-accounts. To retrieve issues for sub-accounts, you must first call the accounts.listSubaccounts method to obtain a list of sub-accounts, and then call `accounts.issues.list` for each sub-account individually.", "flatPath": "accounts/v1beta/accounts/{accountsId}/issues", "httpMethod": "GET", "id": "merchantapi.accounts.issues.list", "parameterOrder": ["parent"], "parameters": {"languageCode": {"description": "Optional. The issues in the response will have human-readable fields in the given language. The format is [BCP-47](https://tools.ietf.org/html/bcp47), such as `en-US` or `sr-Latn`. If not value is provided, `en-US` will be used.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of issues to return. The service may return fewer than this value. If unspecified, at most 50 issues will be returned. The maximum value is 100; values above 100 will be coerced to 100", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListAccountIssues` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccountIssues` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of issues. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "timeZone": {"description": "Optional. The [IANA](https://www.iana.org/time-zones) timezone used to localize times in human-readable fields. For example 'America/Los_Angeles'. If not set, 'America/Los_Angeles' will be used.", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+parent}/issues", "response": {"$ref": "ListAccountIssuesResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "omnichannelSettings": {"methods": {"create": {"description": "Create the omnichannel settings for a given merchant.", "flatPath": "accounts/v1beta/accounts/{accountsId}/omnichannelSettings", "httpMethod": "POST", "id": "merchantapi.accounts.omnichannelSettings.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this omnichannel setting will be created. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/omnichannelSettings", "request": {"$ref": "OmnichannelSetting"}, "response": {"$ref": "OmnichannelSetting"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Get the omnichannel settings for a given merchant.", "flatPath": "accounts/v1beta/accounts/{accountsId}/omnichannelSettings/{omnichannelSettingsId}", "httpMethod": "GET", "id": "merchantapi.accounts.omnichannelSettings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the omnichannel setting to retrieve. Format: `accounts/{account}/omnichannelSettings/{omnichannel_setting}`", "location": "path", "pattern": "^accounts/[^/]+/omnichannelSettings/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "OmnichannelSetting"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "List all the omnichannel settings for a given merchant.", "flatPath": "accounts/v1beta/accounts/{accountsId}/omnichannelSettings", "httpMethod": "GET", "id": "merchantapi.accounts.omnichannelSettings.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of omnichannel settings to return. The service may return fewer than this value. If unspecified, at most 50 omnichannel settings will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListOmnichannelSettings` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListOmnichannelSettings` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of omnichannel settings. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/omnichannelSettings", "response": {"$ref": "ListOmnichannelSettingsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Update the omnichannel setting for a given merchant in a given country.", "flatPath": "accounts/v1beta/accounts/{accountsId}/omnichannelSettings/{omnichannelSettingsId}", "httpMethod": "PATCH", "id": "merchantapi.accounts.omnichannelSettings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the omnichannel setting. Format: `accounts/{account}/omnichannelSettings/{omnichannel_setting}`", "location": "path", "pattern": "^accounts/[^/]+/omnichannelSettings/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. The following fields are supported in snake_case only: - `lsf_type` - `in_stock` - `pickup` - `odo` - `about` - `inventory_verification` Full replacement with wildcard `*`is supported, while empty/implied update mask is not.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "OmnichannelSetting"}, "response": {"$ref": "OmnichannelSetting"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "requestInventoryVerification": {"description": "Requests inventory verification for a given merchant in a given country.", "flatPath": "accounts/v1beta/accounts/{accountsId}/omnichannelSettings/{omnichannelSettingsId}:requestInventoryVerification", "httpMethod": "POST", "id": "merchantapi.accounts.omnichannelSettings.requestInventoryVerification", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the omnichannel setting to request inventory verification. Format: `accounts/{account}/omnichannelSettings/{omnichannel_setting}`", "location": "path", "pattern": "^accounts/[^/]+/omnichannelSettings/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}:requestInventoryVerification", "request": {"$ref": "RequestInventoryVerificationRequest"}, "response": {"$ref": "RequestInventoryVerificationResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}, "resources": {"lfpProviders": {"methods": {"find": {"description": "Find the LFP provider candidates in a given country.", "flatPath": "accounts/v1beta/accounts/{accountsId}/omnichannelSettings/{omnichannelSettingsId}/lfpProviders:find", "httpMethod": "GET", "id": "merchantapi.accounts.omnichannelSettings.lfpProviders.find", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of `LfpProvider` resources to return. The service returns fewer than this value if the number of lfp providers is less that than the `pageSize`. The default value is 50. The maximum value is 1000; If a value higher than the maximum is specified, then the `pageSize` will default to the maximum.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `FindLfpProviders` call. Provide the page token to retrieve the subsequent page. When paginating, all other parameters provided to `FindLfpProviders` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource under which the LFP providers are found. Format: `accounts/{account}/omnichannelSettings/{omnichannel_setting}`.", "location": "path", "pattern": "^accounts/[^/]+/omnichannelSettings/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/lfpProviders:find", "response": {"$ref": "FindLfpProvidersResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "linkLfpProvider": {"description": "Link the specified merchant to a LFP provider for the specified country.", "flatPath": "accounts/v1beta/accounts/{accountsId}/omnichannelSettings/{omnichannelSettingsId}/lfpProviders/{lfpProvidersId}:linkLfpProvider", "httpMethod": "POST", "id": "merchantapi.accounts.omnichannelSettings.lfpProviders.linkLfpProvider", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the LFP provider resource to link. Format: `accounts/{account}/omnichannelSettings/{omnichannel_setting}/lfpProviders/{lfp_provider}`. The `lfp_provider` is the LFP provider ID.", "location": "path", "pattern": "^accounts/[^/]+/omnichannelSettings/[^/]+/lfpProviders/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}:linkLfpProvider", "request": {"$ref": "LinkLfpProviderRequest"}, "response": {"$ref": "LinkLfpProviderResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}, "onlineReturnPolicies": {"methods": {"create": {"description": "Creates a new return policy for a given business.", "flatPath": "accounts/v1beta/accounts/{accountsId}/onlineReturnPolicies", "httpMethod": "POST", "id": "merchantapi.accounts.onlineReturnPolicies.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The Merchant Center account for which the return policy will be created. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/onlineReturnPolicies", "request": {"$ref": "OnlineReturnPolicy"}, "response": {"$ref": "OnlineReturnPolicy"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes an existing return policy.", "flatPath": "accounts/v1beta/accounts/{accountsId}/onlineReturnPolicies/{onlineReturnPoliciesId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.onlineReturnPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the return policy to delete. Format: `accounts/{account}/onlineReturnPolicies/{return_policy}`", "location": "path", "pattern": "^accounts/[^/]+/onlineReturnPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Gets an existing return policy for a given business.", "flatPath": "accounts/v1beta/accounts/{accountsId}/onlineReturnPolicies/{onlineReturnPoliciesId}", "httpMethod": "GET", "id": "merchantapi.accounts.onlineReturnPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the return policy to retrieve. Format: `accounts/{account}/onlineReturnPolicies/{return_policy}`", "location": "path", "pattern": "^accounts/[^/]+/onlineReturnPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "OnlineReturnPolicy"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists all existing return policies for a given business.", "flatPath": "accounts/v1beta/accounts/{accountsId}/onlineReturnPolicies", "httpMethod": "GET", "id": "merchantapi.accounts.onlineReturnPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of `OnlineReturnPolicy` resources to return. The service returns fewer than this value if the number of return policies for the given business is less that than the `pageSize`. The default value is 10. The maximum value is 100; If a value higher than the maximum is specified, then the `pageSize` will default to the maximum", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListOnlineReturnPolicies` call. Provide the page token to retrieve the subsequent page. When paginating, all other parameters provided to `ListOnlineReturnPolicies` must match the call that provided the page token. The token returned as nextPageToken in the response to the previous request.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Merchant Center account for which to list return policies. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/onlineReturnPolicies", "response": {"$ref": "ListOnlineReturnPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates an existing return policy for a given business.", "flatPath": "accounts/v1beta/accounts/{accountsId}/onlineReturnPolicies/{onlineReturnPoliciesId}", "httpMethod": "PATCH", "id": "merchantapi.accounts.onlineReturnPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the `OnlineReturnPolicy` resource. Format: `accounts/{account}/onlineReturnPolicies/{return_policy}`", "location": "path", "pattern": "^accounts/[^/]+/onlineReturnPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `accept_defective_only` - `accept_exchange` - `item_conditions` - `policy` - `process_refund_days` - `restocking_fee` - `return_methods` - `return_policy_uri` - `return_shipping_fee`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "OnlineReturnPolicy"}, "response": {"$ref": "OnlineReturnPolicy"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "programs": {"methods": {"disable": {"description": "Disable participation in the specified program for the account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/programs/{programsId}:disable", "httpMethod": "POST", "id": "merchantapi.accounts.programs.disable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the program for which to disable participation for the given account. Format: `accounts/{account}/programs/{program}`. For example, `accounts/123456/programs/free-listings`.", "location": "path", "pattern": "^accounts/[^/]+/programs/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}:disable", "request": {"$ref": "DisableProgramRequest"}, "response": {"$ref": "Program"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "enable": {"description": "Enable participation in the specified program for the account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/programs/{programsId}:enable", "httpMethod": "POST", "id": "merchantapi.accounts.programs.enable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the program for which to enable participation for the given account. Format: `accounts/{account}/programs/{program}`. For example, `accounts/123456/programs/free-listings`.", "location": "path", "pattern": "^accounts/[^/]+/programs/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}:enable", "request": {"$ref": "EnableProgramRequest"}, "response": {"$ref": "Program"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves the specified program for the account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/programs/{programsId}", "httpMethod": "GET", "id": "merchantapi.accounts.programs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the program to retrieve. Format: `accounts/{account}/programs/{program}`. For example, `accounts/123456/programs/free-listings`.", "location": "path", "pattern": "^accounts/[^/]+/programs/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Program"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Retrieves all programs for the account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/programs", "httpMethod": "GET", "id": "merchantapi.accounts.programs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of programs to return in a single response. If unspecified (or 0), a default size of 1000 is used. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A continuation token, received from a previous `ListPrograms` call. Provide this to retrieve the next page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the account for which to retrieve all programs. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/programs", "response": {"$ref": "ListProgramsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}, "resources": {"checkoutSettings": {"methods": {"create": {"description": "Creates `CheckoutSettings` for the given merchant.", "flatPath": "accounts/v1beta/accounts/{accountsId}/programs/{programsId}/checkoutSettings", "httpMethod": "POST", "id": "merchantapi.accounts.programs.checkoutSettings.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The merchant account for which the `CheckoutSettings` will be created.", "location": "path", "pattern": "^accounts/[^/]+/programs/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/checkoutSettings", "request": {"$ref": "CheckoutSettings"}, "response": {"$ref": "CheckoutSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "deleteCheckoutSettings": {"description": "Deletes `CheckoutSettings` and unenrolls merchant from `Checkout` program.", "flatPath": "accounts/v1beta/accounts/{accountsId}/programs/{programsId}/checkoutSettings", "httpMethod": "DELETE", "id": "merchantapi.accounts.programs.checkoutSettings.deleteCheckoutSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name/identifier of the merchant account. Format: `accounts/{account}/programs/{program}/checkoutSettings`", "location": "path", "pattern": "^accounts/[^/]+/programs/[^/]+/checkoutSettings$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "getCheckoutSettings": {"description": "Gets `CheckoutSettings` for the given merchant. This includes information about review state, enrollment state and URL settings.", "flatPath": "accounts/v1beta/accounts/{accountsId}/programs/{programsId}/checkoutSettings", "httpMethod": "GET", "id": "merchantapi.accounts.programs.checkoutSettings.getCheckoutSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name/identifier of the merchant account. Format: `accounts/{account}/programs/{program}/checkoutSettings`", "location": "path", "pattern": "^accounts/[^/]+/programs/[^/]+/checkoutSettings$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "CheckoutSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateCheckoutSettings": {"description": "Updates `CheckoutSettings` for the given merchant.", "flatPath": "accounts/v1beta/accounts/{accountsId}/programs/{programsId}/checkoutSettings", "httpMethod": "PATCH", "id": "merchantapi.accounts.programs.checkoutSettings.updateCheckoutSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the program configuration settings. Format: `accounts/{account}/programs/{program}/checkoutSettings`", "location": "path", "pattern": "^accounts/[^/]+/programs/[^/]+/checkoutSettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `eligible_destinations` - `uri_settings`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "CheckoutSettings"}, "response": {"$ref": "CheckoutSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}, "regions": {"methods": {"create": {"description": "Creates a region definition in your Merchant Center account. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/regions", "httpMethod": "POST", "id": "merchantapi.accounts.regions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account to create a region for. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "regionId": {"description": "Required. The identifier for the region, unique over all regions of the same account.", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+parent}/regions", "request": {"$ref": "Region"}, "response": {"$ref": "Region"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a region definition from your Merchant Center account. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/regions/{regionsId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.regions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the region to delete. Format: `accounts/{account}/regions/{region}`", "location": "path", "pattern": "^accounts/[^/]+/regions/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves a region defined in your Merchant Center account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/regions/{regionsId}", "httpMethod": "GET", "id": "merchantapi.accounts.regions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the region to retrieve. Format: `accounts/{account}/regions/{region}`", "location": "path", "pattern": "^accounts/[^/]+/regions/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Region"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the regions in your Merchant Center account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/regions", "httpMethod": "GET", "id": "merchantapi.accounts.regions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of regions to return. The service may return fewer than this value. If unspecified, at most 50 regions will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListRegions` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListRegions` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account to list regions for. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/regions", "response": {"$ref": "ListRegionsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates a region definition in your Merchant Center account. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/regions/{regionsId}", "httpMethod": "PATCH", "id": "merchantapi.accounts.regions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the region. Format: `accounts/{account}/regions/{region}`", "location": "path", "pattern": "^accounts/[^/]+/regions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The comma-separated field mask indicating the fields to update. Example: `\"displayName,postalCodeArea.regionCode\"`.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "Region"}, "response": {"$ref": "Region"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "relationships": {"methods": {"get": {"description": "Retrieve an account relationship.", "flatPath": "accounts/v1beta/accounts/{accountsId}/relationships/{relationshipsId}", "httpMethod": "GET", "id": "merchantapi.accounts.relationships.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the account relationship to get. Format: `accounts/{account}/relationships/{relationship}`. For example, `accounts/123456/relationships/567890`.", "location": "path", "pattern": "^accounts/[^/]+/relationships/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "AccountRelationship"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "List account relationships for the specified account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/relationships", "httpMethod": "GET", "id": "merchantapi.accounts.relationships.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of elements to return in the response. Use for paging. If no `page_size` is specified, `100` is used as the default value. The maximum allowed value is `1000`.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The token returned by the previous `list` request.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent account of the account relationship to filter by. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/relationships", "response": {"$ref": "ListAccountRelationshipsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates the account relationship. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/relationships/{relationshipsId}", "httpMethod": "PATCH", "id": "merchantapi.accounts.relationships.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the account relationship. Format: `accounts/{account}/relationships/{relationship}`. For example, `accounts/123456/relationships/567890`.", "location": "path", "pattern": "^accounts/[^/]+/relationships/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `account_id_alias`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "AccountRelationship"}, "response": {"$ref": "AccountRelationship"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "services": {"methods": {"approve": {"description": "Approve an account service proposal.", "flatPath": "accounts/v1beta/accounts/{accountsId}/services/{servicesId}:approve", "httpMethod": "POST", "id": "merchantapi.accounts.services.approve", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the account service to approve. Format: `accounts/{account}/services/{service}`", "location": "path", "pattern": "^accounts/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}:approve", "request": {"$ref": "ApproveAccountServiceRequest"}, "response": {"$ref": "AccountService"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieve an account service.", "flatPath": "accounts/v1beta/accounts/{accountsId}/services/{servicesId}", "httpMethod": "GET", "id": "merchantapi.accounts.services.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the account service to get. Format: `accounts/{account}/services/{service}`", "location": "path", "pattern": "^accounts/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "AccountService"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "List account services for the specified accounts. Supports filtering.", "flatPath": "accounts/v1beta/accounts/{accountsId}/services", "httpMethod": "GET", "id": "merchantapi.accounts.services.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of elements to return in the response. Use for paging. If no `page_size` is specified, `100` is used as the default value. The maximum allowed value is `1000`.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The token returned by the previous `list` request.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent account of the account service to filter by. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/services", "response": {"$ref": "ListAccountServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "propose": {"description": "Propose an account service.", "flatPath": "accounts/v1beta/accounts/{accountsId}/services:propose", "httpMethod": "POST", "id": "merchantapi.accounts.services.propose", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the parent account for the service. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/services:propose", "request": {"$ref": "ProposeAccountServiceRequest"}, "response": {"$ref": "AccountService"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "reject": {"description": "Reject an account service (both proposed and approve services can be rejected).", "flatPath": "accounts/v1beta/accounts/{accountsId}/services/{servicesId}:reject", "httpMethod": "POST", "id": "merchantapi.accounts.services.reject", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the account service to reject. Format: `accounts/{account}/services/{service}`", "location": "path", "pattern": "^accounts/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}:reject", "request": {"$ref": "RejectAccountServiceRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "shippingSettings": {"methods": {"getShippingSettings": {"description": "Retrieve shipping setting information.", "flatPath": "accounts/v1beta/accounts/{accountsId}/shippingSettings", "httpMethod": "GET", "id": "merchantapi.accounts.shippingSettings.getShippingSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the shipping setting to retrieve. Format: `accounts/{account}/shippingsettings`", "location": "path", "pattern": "^accounts/[^/]+/shippingSettings$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "ShippingSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Replace the shipping setting of a business with the request shipping setting. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/shippingSettings:insert", "httpMethod": "POST", "id": "merchantapi.accounts.shippingSettings.insert", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account for which this shipping setting will be inserted. If you are using an advanced account, you must specify the unique identifier of the sub-account for which you want to insert the shipping setting. Format: `accounts/{ACCOUNT_ID}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/shippingSettings:insert", "request": {"$ref": "ShippingSettings"}, "response": {"$ref": "ShippingSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "termsOfServiceAgreementStates": {"methods": {"get": {"description": "Returns the state of a terms of service agreement.", "flatPath": "accounts/v1beta/accounts/{accountsId}/termsOfServiceAgreementStates/{termsOfServiceAgreementStatesId}", "httpMethod": "GET", "id": "merchantapi.accounts.termsOfServiceAgreementStates.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the terms of service version. Format: `accounts/{account}/termsOfServiceAgreementStates/{identifier}` The identifier format is: `{TermsOfServiceKind}-{country}`", "location": "path", "pattern": "^accounts/[^/]+/termsOfServiceAgreementStates/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "TermsOfServiceAgreementState"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "retrieveForApplication": {"description": "Retrieves the state of the agreement for the application terms of service. Application terms of service covers permissions related to the usage of data provided through Merchant Center, CSS Center, Manufacturer Center, and more.", "flatPath": "accounts/v1beta/accounts/{accountsId}/termsOfServiceAgreementStates:retrieveForApplication", "httpMethod": "GET", "id": "merchantapi.accounts.termsOfServiceAgreementStates.retrieveForApplication", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account for which to get a TermsOfServiceAgreementState Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/termsOfServiceAgreementStates:retrieveForApplication", "response": {"$ref": "TermsOfServiceAgreementState"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "users": {"methods": {"create": {"description": "Creates a Merchant Center account user. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/users", "httpMethod": "POST", "id": "merchantapi.accounts.users.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the account for which a user will be created. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "userId": {"description": "Required. The email address of the user (for example, `<EMAIL>`).", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+parent}/users", "request": {"$ref": "User"}, "response": {"$ref": "User"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a Merchant Center account user. Executing this method requires admin access. The user to be deleted can't be the last admin user of that account. Also a user is protected from deletion if it is managed by Business Manager\"", "flatPath": "accounts/v1beta/accounts/{accountsId}/users/{usersId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.users.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the user to delete. Format: `accounts/{account}/users/{email}` It is also possible to delete the user corresponding to the caller by using `me` rather than an email address as in `accounts/{account}/users/me`.", "location": "path", "pattern": "^accounts/[^/]+/users/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves a Merchant Center account user.", "flatPath": "accounts/v1beta/accounts/{accountsId}/users/{usersId}", "httpMethod": "GET", "id": "merchantapi.accounts.users.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the user to retrieve. Format: `accounts/{account}/users/{email}` It is also possible to retrieve the user corresponding to the caller by using `me` rather than an email address as in `accounts/{account}/users/me`.", "location": "path", "pattern": "^accounts/[^/]+/users/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "User"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists all users of a Merchant Center account.", "flatPath": "accounts/v1beta/accounts/{accountsId}/users", "httpMethod": "GET", "id": "merchantapi.accounts.users.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of users to return. The service may return fewer than this value. If unspecified, at most 50 users will be returned. The maximum value is 100; values above 100 will be coerced to 100", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListUsers` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListUsers` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of users. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+parent}/users", "response": {"$ref": "ListUsersResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates a Merchant Center account user. Executing this method requires admin access.", "flatPath": "accounts/v1beta/accounts/{accountsId}/users/{usersId}", "httpMethod": "PATCH", "id": "merchantapi.accounts.users.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the user. Format: `accounts/{account}/user/{email}` Use `me` to refer to your own email address, for example `accounts/{account}/users/me`.", "location": "path", "pattern": "^accounts/[^/]+/users/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. List of fields being updated. The following fields are supported (in both `snake_case` and `lowerCamelCase`): - `access_rights`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}", "request": {"$ref": "User"}, "response": {"$ref": "User"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}, "termsOfService": {"methods": {"accept": {"description": "Accepts a `TermsOfService`. Executing this method requires admin access.", "flatPath": "accounts/v1beta/termsOfService/{termsOfServiceId}:accept", "httpMethod": "POST", "id": "merchantapi.termsOfService.accept", "parameterOrder": ["name"], "parameters": {"account": {"description": "Required. The account for which to accept the ToS. Format: `accounts/{account}`", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the terms of service version. Format: `termsOfService/{version}`", "location": "path", "pattern": "^termsOfService/[^/]+$", "required": true, "type": "string"}, "regionCode": {"description": "Required. Region code as defined by [CLDR](https://cldr.unicode.org/). This is either a country when the ToS applies specifically to that country or 001 when it applies globally.", "location": "query", "type": "string"}}, "path": "accounts/v1beta/{+name}:accept", "response": {"$ref": "AcceptTermsOfServiceResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves the `TermsOfService` associated with the provided version.", "flatPath": "accounts/v1beta/termsOfService/{termsOfServiceId}", "httpMethod": "GET", "id": "merchantapi.termsOfService.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the terms of service version. Format: `termsOfService/{version}`", "location": "path", "pattern": "^termsOfService/[^/]+$", "required": true, "type": "string"}}, "path": "accounts/v1beta/{+name}", "response": {"$ref": "TermsOfService"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "retrieveLatest": {"description": "Retrieves the latest version of the `TermsOfService` for a given `kind` and `region_code`.", "flatPath": "accounts/v1beta/termsOfService:retrieveLatest", "httpMethod": "GET", "id": "merchantapi.termsOfService.retrieveLatest", "parameterOrder": [], "parameters": {"kind": {"description": "Required. The Kind this terms of service version applies to.", "enum": ["TERMS_OF_SERVICE_KIND_UNSPECIFIED", "MERCHANT_CENTER"], "enumDescriptions": ["Default value. This value is unused.", "Merchant Center application."], "location": "query", "type": "string"}, "regionCode": {"description": "Required. Region code as defined by [CLDR](https://cldr.unicode.org/). This is either a country when the ToS applies specifically to that country or 001 when it applies globally.", "location": "query", "type": "string"}}, "path": "accounts/v1beta/termsOfService:retrieveLatest", "response": {"$ref": "TermsOfService"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}, "revision": "********", "rootUrl": "https://merchantapi.googleapis.com/", "schemas": {"About": {"description": "Collection of information related to the about page ([impressum](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********&sjid=6892280366904591178-NC)).", "id": "About", "properties": {"state": {"description": "Output only. The state of the URI.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "FAILED", "RUNNING", "ACTION_REQUIRED"], "enumDescriptions": ["Default value. This value is unused.", "The review process has concluded successfully. The reviewed item is active.", "The review process failed.", "The review process is running.", "The review process is waiting for the merchant to take action."], "readOnly": true, "type": "string"}, "uri": {"description": "Required. The about page URI.", "type": "string"}}, "type": "object"}, "AcceptTermsOfServiceResponse": {"description": "Response message for the `AcceptTermsOfService` method.", "id": "AcceptTermsOfServiceResponse", "properties": {"termsOfServiceAgreementState": {"$ref": "TermsOfServiceAgreementState", "description": "The agreement state after accepting the ToS."}}, "type": "object"}, "Accepted": {"description": "Describes the [accepted terms of service](/merchant/api/guides/accounts/create-and-configure#accept_the_merchant_center_terms_of_service).", "id": "Accepted", "properties": {"acceptedBy": {"description": "Required. The account where the acceptance was recorded. This can be the account itself or, in the case of subaccounts, the advanced account.", "type": "string"}, "termsOfService": {"description": "Required. The accepted termsOfService.", "type": "string"}, "validUntil": {"$ref": "Date", "description": "Optional. When set, it states that the accepted `TermsOfService` is only valid until the end of this date (in UTC). A new one must be accepted before then. The information of the required `TermsOfService` is found in the `Required` message."}}, "type": "object"}, "Account": {"description": "The `Account` message represents a business's account within Shopping Ads. It's the primary entity for managing product data, settings, and interactions with Google's services and external providers. Accounts can operate as standalone entities or be part of a advanced account structure. In an advanced account setup the parent account manages multiple sub-accounts. Establishing an account involves configuring attributes like the account name, time zone, and language preferences. The `Account` message is the parent entity for many other resources, for example, `AccountRelationship`, `Homepage`, `BusinessInfo` and so on.", "id": "Account", "properties": {"accountId": {"description": "Output only. The ID of the account.", "format": "int64", "readOnly": true, "type": "string"}, "accountName": {"description": "Required. A human-readable name of the account. See [store name](https://support.google.com/merchants/answer/160556) and [business name](https://support.google.com/merchants/answer/********) for more information.", "type": "string"}, "adultContent": {"description": "Optional. Whether this account contains adult content.", "type": "boolean"}, "languageCode": {"description": "Required. The account's [BCP-47 language code](https://tools.ietf.org/html/bcp47), such as `en-US` or `sr-Latn`.", "type": "string"}, "name": {"description": "Identifier. The resource name of the account. Format: `accounts/{account}`", "type": "string"}, "testAccount": {"description": "Output only. Whether this is a test account.", "readOnly": true, "type": "boolean"}, "timeZone": {"$ref": "TimeZone", "description": "Required. The time zone of the account. On writes, `time_zone` sets both the `reporting_time_zone` and the `display_time_zone`. For reads, `time_zone` always returns the `display_time_zone`. If `display_time_zone` doesn't exist for your account, `time_zone` is empty. The `version` field is not supported, won't be set in responses and will be silently ignored if specified in requests."}}, "type": "object"}, "AccountAggregation": {"description": "`AccountAggregation` payload.", "id": "AccountAggregation", "properties": {}, "type": "object"}, "AccountIssue": {"description": "Issues with your Merchant Center account that can impact all your products. For more information, see [Account-level issues in Merchant Center](https://support.google.com/merchants/answer/********?sjid=17798438912526418908-EU#account).", "id": "Account<PERSON>ssue", "properties": {"detail": {"description": "Further localized details about the issue.", "type": "string"}, "documentationUri": {"description": "Link to Merchant Center Help Center providing further information about the issue and how to fix it.", "type": "string"}, "impactedDestinations": {"description": "The impact this issue has on various destinations.", "items": {"$ref": "ImpactedDestination"}, "type": "array"}, "name": {"description": "Identifier. The resource name of the account issue. Format: `accounts/{account}/issues/{id}`. For example, `accounts/123456/issues/misrepresentation-of-self-or-products-unacceptable-business-practice-policy`.", "type": "string"}, "severity": {"description": "The overall severity of the issue.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "ERROR", "SUGGESTION"], "enumDescriptions": ["The severity is unknown.", "The issue causes offers to not serve.", "The issue might affect offers (in the future) or might be an indicator of issues with offers.", "The issue is a suggestion for improvement."], "type": "string"}, "title": {"description": "The localized title of the issue.", "type": "string"}}, "type": "object"}, "AccountManagement": {"description": "`AccountManagement` payload.", "id": "AccountManagement", "properties": {}, "type": "object"}, "AccountRelationship": {"description": "The `AccountRelationship` message defines a formal connection between a merchant's account and a service provider's account. This relationship enables the provider to offer specific services to the business, such as product management or campaign management. It specifies the access rights and permissions to the business's data relevant to those services. Establishing an account relationship involves linking the merchant's account with a provider's account. The provider could be another Google account (like Google Ads or Google My Business) or a third-party platform (such as Shopify or WooCommerce).", "id": "AccountRelationship", "properties": {"accountIdAlias": {"description": "Optional. An optional alias you can assign to this account relationship. This alias acts as a convenient identifier for your own reference and management. It must be unique among all your account relationships with the same provider. For example, you might use `account_id_alias` to assign a friendly name to this relationship for easier identification in your systems.", "type": "string"}, "name": {"description": "Identifier. The resource name of the account relationship. Format: `accounts/{account}/relationships/{relationship}`. For example, `accounts/123456/relationships/567890`.", "type": "string"}, "provider": {"description": "Immutable. The provider of the service. Either the reference to an account such as `providers/123` or a well-known service provider (one of `providers/GOOGLE_ADS` or `providers/GOOGLE_BUSINESS_PROFILE`).", "type": "string"}, "providerDisplayName": {"description": "Output only. The human-readable display name of the provider account.", "readOnly": true, "type": "string"}}, "type": "object"}, "AccountService": {"description": "The `AccountService` message represents a specific service that a provider account offers to a Merchant Center account. `AccountService` defines the permissions and capabilities granted to the provider, allowing for operations such as product management or campaign management. The lifecycle of an `AccountService` involves a proposal phase, where one party suggests the service, and an approval phase, where the other party accepts or rejects it. This handshake mechanism ensures mutual consent before any access is granted. This mechanism safeguards both parties by ensuring that access rights are granted appropriately and that both the business and provider are aware of the services enabled. In scenarios where a user is an admin of both accounts, the approval can happen automatically. The mutability of a service is also managed through `AccountService`. Some services might be immutable, for example, if they were established through other systems or APIs, and you cannot alter them through this API.", "id": "AccountService", "properties": {"accountAggregation": {"$ref": "AccountAggregation", "description": "Service type for account aggregation. This enables the provider, which is an advanced account, to manage multiple sub-accounts (client accounts). Through this service, the advanced account provider can perform administrative and operational tasks across all linked sub-accounts. This is useful for agencies, aggregators, or large retailers that need centralized control over many Merchant Center accounts."}, "accountManagement": {"$ref": "AccountManagement", "description": "Service type for account management. Enables the provider to perform administrative actions on the business's account, such as configuring account settings, managing users, or updating business information."}, "campaignsManagement": {"$ref": "CampaignsManagement", "description": "Service type for managing advertising campaigns. Grants the provider access to create and manage the business's ad campaigns, including setting up campaigns, adjusting bids, and optimizing performance."}, "externalAccountId": {"description": "Immutable. An optional, immutable identifier that Google uses to refer to this account when communicating with the provider. This should be the unique account ID within the provider's system (for example, your shop ID in Shopify). If you have multiple accounts with the same provider - for instance, different accounts for various regions — the `external_account_id` differentiates between them, ensuring accurate linking and integration between Google and the provider.", "type": "string"}, "handshake": {"$ref": "Handshake", "description": "Output only. Information about the state of the service in terms of establishing it (e.g. is it pending approval or approved).", "readOnly": true}, "localListingManagement": {"$ref": "LocalListingManagement", "description": "Service type for local listings management. The business group associated with the external account id will be used to provide local inventory to this Merchant Center account."}, "mutability": {"description": "Output only. Whether the service is mutable (e.g. through Approve / Reject RPCs). A service that was created through another system or API might be immutable.", "enum": ["MUTABILITY_UNSPECIFIED", "MUTABLE", "IMMUTABLE"], "enumDescriptions": ["Unused default value", "The service can be mutated without restrictions.", "The service is read-only and must not be mutated."], "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The resource name of the account service. Format: `accounts/{account}/services/{service}`", "type": "string"}, "productsManagement": {"$ref": "ProductsManagement", "description": "Service type for managing products. This allows the provider to handle product data on behalf of the business, including reading and writing product listings. It's commonly used when the provider offers inventory management or catalog synchronization services to keep the business's product information up-to-date across platforms."}, "provider": {"description": "Output only. The provider of the service. Either the reference to an account such as `providers/123` or a well-known service provider (one of `providers/GOOGLE_ADS` or `providers/GOOGLE_BUSINESS_PROFILE`).", "readOnly": true, "type": "string"}, "providerDisplayName": {"description": "Output only. The human-readable display name of the provider account.", "readOnly": true, "type": "string"}}, "type": "object"}, "AddAccountService": {"description": "Additional instructions to add account services during creation of the account.", "id": "AddAccountService", "properties": {"accountAggregation": {"$ref": "AccountAggregation", "description": "The provider is an [aggregator](https://support.google.com/merchants/answer/188487) for the account. Payload for service type Account Aggregation."}, "provider": {"description": "Required. The provider of the service. Either the reference to an account such as `providers/123` or a well-known service provider (one of `providers/GOOGLE_ADS` or `providers/GOOGLE_BUSINESS_PROFILE`).", "type": "string"}}, "type": "object"}, "AddUser": {"description": "Instruction for adding a user to the account during creation.", "id": "AddUser", "properties": {"user": {"$ref": "User", "description": "Optional. Details about the user to be added. At the moment, only access rights may be specified."}, "userId": {"description": "Required. The email address of the user (for example, `<EMAIL>`).", "type": "string"}}, "type": "object"}, "Address": {"description": "Shipping address of the warehouse.", "id": "Address", "properties": {"administrativeArea": {"description": "Required. Top-level administrative subdivision of the country. For example, a state like California (\"CA\") or a province like Quebec (\"QC\").", "type": "string"}, "city": {"description": "Required. City, town or commune. May also include dependent localities or sublocalities (For example neighborhoods or suburbs).", "type": "string"}, "postalCode": {"description": "Required. Postal code or ZIP (For example \"94043\").", "type": "string"}, "regionCode": {"description": "Required. [CLDR country code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml) (For example \"US\").", "type": "string"}, "streetAddress": {"description": "Street-level part of the address. For example: `111w 31st Street`.", "type": "string"}}, "type": "object"}, "ApproveAccountServiceRequest": {"description": "Request to approve an account service.", "id": "ApproveAccountServiceRequest", "properties": {}, "type": "object"}, "AutofeedSettings": {"description": "Collection of information related to the [autofeed](https://support.google.com/merchants/answer/7538732) settings.", "id": "AutofeedSettings", "properties": {"eligible": {"description": "Output only. Determines whether the business is eligible for being enrolled into an autofeed.", "readOnly": true, "type": "boolean"}, "enableProducts": {"description": "Required. Enables or disables product crawling through the autofeed for the given account. Autofeed accounts must meet [certain conditions](https://support.google.com/merchants/answer/7538732#Configure_automated_feeds_Standard_Experience), which can be checked through the `eligible` field. The account must **not** be a marketplace. When the autofeed is enabled for the first time, the products usually appear instantly. When re-enabling, it might take up to 24 hours for products to appear.", "type": "boolean"}, "name": {"description": "Identifier. The resource name of the autofeed settings. Format: `accounts/{account}/autofeedSettings`.", "type": "string"}}, "type": "object"}, "AutomaticImageImprovements": {"description": "This improvement will attempt to automatically correct submitted images if they don't meet the [image requirements](https://support.google.com/merchants/answer/6324350), for example, removing overlays. If successful, the image will be replaced and approved. This improvement is only applied to images of disapproved offers. For more information see: [Automatic image improvements](https://support.google.com/merchants/answer/9242973)", "id": "AutomaticImageImprovements", "properties": {"accountImageImprovementsSettings": {"$ref": "ImageImprovementsAccountLevelSettings", "description": "Optional. Determines how the images should be automatically updated. If this field is not present and provided in the update mask, then the settings will be deleted. If there are no settings for subaccount, they are inherited from aggregator."}, "effectiveAllowAutomaticImageImprovements": {"description": "Output only. The effective value of allow_automatic_image_improvements. If account_image_improvements_settings is present, then this value is the same. Otherwise, it represents the inherited value of the parent account. Read-only.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "AutomaticImprovements": {"description": "Collection of information related to the [automatic improvements](https://developers.google.com/shopping-content/guides/automatic-improvements) of an account.", "id": "AutomaticImprovements", "properties": {"imageImprovements": {"$ref": "AutomaticImageImprovements", "description": "This improvement will attempt to automatically correct submitted images if they don't meet the [image requirements](https://support.google.com/merchants/answer/6324350), for example, removing overlays. If successful, the image will be replaced and approved. This improvement is only applied to images of disapproved offers. For more information see: [Automatic image improvements](https://support.google.com/merchants/answer/9242973) This field is only updated (cleared) if provided in the update mask."}, "itemUpdates": {"$ref": "AutomaticItemUpdates", "description": "Turning on [item updates](https://support.google.com/merchants/answer/3246284) allows Google to automatically update items for you. When item updates are on, Google uses the structured data markup on the website and advanced data extractors to update the price and availability of the items. When the item updates are off, items with mismatched data aren't shown. This field is only updated (cleared) if provided in the update mask."}, "name": {"description": "Identifier. The resource name of the automatic improvements. Format: `accounts/{account}/automaticImprovements`.", "type": "string"}, "shippingImprovements": {"$ref": "AutomaticShippingImprovements", "description": "Not available for [advanced accounts](https://support.google.com/merchants/answer/188487). By turning on [automatic shipping improvements](https://support.google.com/merchants/answer/********), you are allowing Google to improve the accuracy of your delivery times shown to shoppers using Google. More accurate delivery times, especially when faster, typically lead to better conversion rates. Google will improve your estimated delivery times based on various factors: * Delivery address of an order * Current handling time and shipping time settings * Estimated weekdays or business days * Parcel tracking data This field is only updated (cleared) if provided in the update mask."}}, "type": "object"}, "AutomaticItemUpdates": {"description": "Turning on [item updates](https://support.google.com/merchants/answer/3246284) allows Google to automatically update items for you. When item updates are on, Google uses the structured data markup on the website and advanced data extractors to update the price and availability of the items. When the item updates are off, items with mismatched data aren't shown.", "id": "AutomaticItemUpdates", "properties": {"accountItemUpdatesSettings": {"$ref": "ItemUpdatesAccountLevelSettings", "description": "Optional. Determines which attributes of the items should be automatically updated. If this field is not present and provided in the update mask, then the settings will be deleted. If there are no settings for subaccount, they are inherited from aggregator."}, "effectiveAllowAvailabilityUpdates": {"description": "Output only. The effective value of allow_availability_updates. If account_item_updates_settings is present, then this value is the same. Otherwise, it represents the inherited value of the parent account. The default value is true if no settings are present. Read-only.", "readOnly": true, "type": "boolean"}, "effectiveAllowConditionUpdates": {"description": "Output only. The effective value of allow_condition_updates. If account_item_updates_settings is present, then this value is the same. Otherwise, it represents the inherited value of the parent account. The default value is true if no settings are present. Read-only.", "readOnly": true, "type": "boolean"}, "effectiveAllowPriceUpdates": {"description": "Output only. The effective value of allow_price_updates. If account_item_updates_settings is present, then this value is the same. Otherwise, it represents the inherited value of the parent account. The default value is true if no settings are present. Read-only.", "readOnly": true, "type": "boolean"}, "effectiveAllowStrictAvailabilityUpdates": {"description": "Output only. The effective value of allow_strict_availability_updates. If account_item_updates_settings is present, then this value is the same. Otherwise, it represents the inherited value of the parent account. The default value is true if no settings are present. Read-only.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "AutomaticShippingImprovements": {"description": "Not available for [advanced accounts](https://support.google.com/merchants/answer/188487). By turning on [automatic shipping improvements](https://support.google.com/merchants/answer/********), you are allowing Google to improve the accuracy of your delivery times shown to shoppers using Google. More accurate delivery times, especially when faster, typically lead to better conversion rates. Google will improve your estimated delivery times based on various factors: * Delivery address of an order * Current handling time and shipping time settings * Estimated weekdays or business days * Parcel tracking data", "id": "AutomaticShippingImprovements", "properties": {"allowShippingImprovements": {"description": "Enables automatic shipping improvements.", "type": "boolean"}}, "type": "object"}, "BusinessDayConfig": {"description": "Business days of the warehouse.", "id": "BusinessDayConfig", "properties": {"businessDays": {"description": "Required. Regular business days. May not be empty.", "items": {"enum": ["WEEKDAY_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["", "", "", "", "", "", "", ""], "type": "string"}, "type": "array"}}, "type": "object"}, "BusinessIdentity": {"description": "Collection of information related to the [identity of a business](https://support.google.com/merchants/answer/12564247).", "id": "BusinessIdentity", "properties": {"blackOwned": {"$ref": "IdentityAttribute", "description": "Optional. Specifies whether the business identifies itself as being black-owned. This optional field will only be available for businesses with the business country set to `US`. It is also not applicable for marketplaces or marketplace sellers."}, "latinoOwned": {"$ref": "IdentityAttribute", "description": "Optional. Specifies whether the business identifies itself as being latino-owned. This optional field will only be available for businesses with the business country set to `US`. It is also not applicable for marketplaces or marketplace sellers."}, "name": {"description": "Identifier. The resource name of the business identity. Format: `accounts/{account}/businessIdentity`", "type": "string"}, "promotionsConsent": {"description": "Required. Whether the identity attributes may be used for promotions.", "enum": ["PROMOTIONS_CONSENT_UNSPECIFIED", "PROMOTIONS_CONSENT_GIVEN", "PROMOTIONS_CONSENT_DENIED"], "enumDescriptions": ["Default value indicating that no selection was made.", "Indicates that the account consented to having their business identity used for promotions.", "Indicates that the account did not consent to having their business identity used for promotions."], "type": "string"}, "smallBusiness": {"$ref": "IdentityAttribute", "description": "Optional. Specifies whether the business identifies itself as a small business. This optional field will only be available for businesses with a business country set to `US`. It is also not applicable for marketplaces."}, "veteranOwned": {"$ref": "IdentityAttribute", "description": "Optional. Specifies whether the business identifies itself as being veteran-owned. This optional field will only be available for businesses with a business country set to `US`. It is also not applicable for marketplaces or marketplace sellers."}, "womenOwned": {"$ref": "IdentityAttribute", "description": "Optional. Specifies whether the business identifies itself as being women-owned. This optional field will only be available for businesses with a business country set to `US`. It is also not applicable for marketplaces or marketplace sellers."}}, "type": "object"}, "BusinessInfo": {"description": "The `BusinessInfo` message contains essential information about a business. This message captures key business details such as physical address, customer service contacts, and region-specific identifiers.", "id": "BusinessInfo", "properties": {"address": {"$ref": "PostalAddress", "description": "Optional. The address of the business. Only `region_code`, `address_lines`, `postal_code`, `administrative_area` and `locality` fields are supported. All other fields are ignored."}, "customerService": {"$ref": "CustomerService", "description": "Optional. The customer service of the business."}, "koreanBusinessRegistrationNumber": {"description": "Optional. The 10-digit [Korean business registration number](https://support.google.com/merchants/answer/9037766) separated with dashes in the format: XXX-XX-XXXXX.", "type": "string"}, "name": {"description": "Identifier. The resource name of the business info. Format: `accounts/{account}/businessInfo`", "type": "string"}, "phone": {"$ref": "PhoneNumber", "description": "Output only. The phone number of the business.", "readOnly": true}, "phoneVerificationState": {"description": "Output only. The phone verification state of the business.", "enum": ["PHONE_VERIFICATION_STATE_UNSPECIFIED", "PHONE_VERIFICATION_STATE_VERIFIED", "PHONE_VERIFICATION_STATE_UNVERIFIED"], "enumDescriptions": ["Default value. This value is unused.", "The phone is verified.", "The phone is unverified."], "readOnly": true, "type": "string"}}, "type": "object"}, "CampaignsManagement": {"description": "`CampaignManagement` payload.", "id": "CampaignsManagement", "properties": {}, "type": "object"}, "CarrierRate": {"description": "A list of carrier rates that can be referred to by `main_table` or `single_value`. Supported carrier services are defined in https://support.google.com/merchants/answer/********?ref_topic=********&sjid=10662598224319463032-NC#zippy=%2Cdelivery-cost-rate-type%2Ccarrier-rate-au-de-uk-and-us-only.", "id": "CarrierRate", "properties": {"carrier": {"description": "Required. Carrier service, such as `\"UPS\"` or `\"Fedex\"`.", "type": "string"}, "carrierService": {"description": "Required. Carrier service, such as `\"ground\"` or `\"2 days\"`.", "type": "string"}, "flatAdjustment": {"$ref": "Price", "description": "Optional. Additive shipping rate modifier. Can be negative. For example `{ \"amount_micros\": 1, \"currency_code\" : \"USD\" }` adds $1 to the rate, `{ \"amount_micros\": -3, \"currency_code\" : \"USD\" }` removes $3 from the rate."}, "name": {"description": "Required. Name of the carrier rate. Must be unique per rate group.", "type": "string"}, "originPostalCode": {"description": "Required. Shipping origin for this carrier rate.", "type": "string"}, "percentageAdjustment": {"description": "Optional. Multiplicative shipping rate modifier as a number in decimal notation. Can be negative. For example `\"5.4\"` increases the rate by 5.4%, `\"-3\"` decreases the rate by 3%.", "type": "string"}}, "type": "object"}, "CheckoutSettings": {"description": "[CheckoutSettings](https://support.google.com/merchants/answer/********) for a specific merchant.", "id": "CheckoutSettings", "properties": {"effectiveEnrollmentState": {"description": "Output only. The effective value of enrollment_state for a given merchant ID. If account level settings are present then this value will be a copy of the account level settings. Otherwise, it will have the value of the parent account (for only marketplace sellers).", "enum": ["CHECKOUT_ENROLLMENT_STATE_UNSPECIFIED", "INACTIVE", "ENROLLED", "OPTED_OUT"], "enumDescriptions": ["Default enrollment state when enrollment state is not specified.", "<PERSON> has not enrolled into the program.", "<PERSON> has enrolled into the program by providing either an account level URL or checkout URLs as part of their feed.", "<PERSON> has previously enrolled but opted out of the program."], "readOnly": true, "type": "string"}, "effectiveReviewState": {"description": "Output only. The effective value of `review_state` for a given merchant ID. If account level settings are present then this value will be a copy of the account level settings. Otherwise, it will have the value of the parent account (for only marketplace sellers).", "enum": ["CHECKOUT_REVIEW_STATE_UNSPECIFIED", "IN_REVIEW", "APPROVED", "DISAPPROVED"], "enumDescriptions": ["Default review state when review state is not specified.", "Merchant provided URLs are being reviewed for data quality issues.", "Merchant account has been approved. Indicates the data quality checks have passed.", "Merchant account has been disapproved due to data quality issues."], "readOnly": true, "type": "string"}, "effectiveUriSettings": {"$ref": "UriSettings", "description": "Output only. The effective value of `uri_settings` for a given merchant. If account level settings are present then this value will be a copy of url settings. Otherwise, it will have the value of the parent account (for only marketplace sellers).", "readOnly": true}, "eligibleDestinations": {"description": "Optional. The destinations to which the checkout program applies, valid destination values are `SHOPPING_ADS`, `FREE_LISTINGS`", "items": {"enum": ["DESTINATION_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "YOUTUBE_SHOPPING", "YOUTUBE_SHOPPING_CHECKOUT", "YOUTUBE_AFFILIATE", "FREE_VEHICLE_LISTINGS", "VEHICLE_ADS", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL"], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/google-ads/answer/2454022).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3057972).", "[Free listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[YouTube Shopping](https://support.google.com/merchants/answer/12362804).", "Youtube shopping checkout.", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[Free vehicle listings](https://support.google.com/merchants/answer/11189169).", "[Vehicle ads](https://support.google.com/merchants/answer/11189169).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail)."], "type": "string"}, "type": "array"}, "enrollmentState": {"description": "Output only. Reflects the merchant enrollment state in `Checkout` program.", "enum": ["CHECKOUT_ENROLLMENT_STATE_UNSPECIFIED", "INACTIVE", "ENROLLED", "OPTED_OUT"], "enumDescriptions": ["Default enrollment state when enrollment state is not specified.", "<PERSON> has not enrolled into the program.", "<PERSON> has enrolled into the program by providing either an account level URL or checkout URLs as part of their feed.", "<PERSON> has previously enrolled but opted out of the program."], "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The resource name of the program configuration settings. Format: `accounts/{account}/programs/{program}/checkoutSettings`", "type": "string"}, "reviewState": {"description": "Output only. Reflects the merchant review state in `Checkout` program. This is set based on the data quality reviews of the URL provided by the merchant. A merchant with enrollment state as `ENROLLED` can be in the following review states: `IN_REVIEW`, `APPROVED` or `DISAPPROVED`. A merchant must be in an `enrollment_state` of `ENROLLED` before a review can begin for the merchant.For more details, check the help center doc.", "enum": ["CHECKOUT_REVIEW_STATE_UNSPECIFIED", "IN_REVIEW", "APPROVED", "DISAPPROVED"], "enumDescriptions": ["Default review state when review state is not specified.", "Merchant provided URLs are being reviewed for data quality issues.", "Merchant account has been approved. Indicates the data quality checks have passed.", "Merchant account has been disapproved due to data quality issues."], "readOnly": true, "type": "string"}, "uriSettings": {"$ref": "UriSettings", "description": "URI settings for cart or checkout URL."}}, "type": "object"}, "ClaimHomepageRequest": {"description": "Request message for the `ClaimHomepage` method.", "id": "ClaimHomepageRequest", "properties": {"overwrite": {"description": "Optional. When set to `true`, this option removes any existing claim on the requested website from any other account to the account making the request, effectively replacing the previous claim.", "type": "boolean"}}, "type": "object"}, "CreateAndConfigureAccountRequest": {"description": "Request message for the `CreateAndConfigureAccount` method.", "id": "CreateAndConfigureAccountRequest", "properties": {"account": {"$ref": "Account", "description": "Required. The account to be created."}, "service": {"description": "Required. An account service between the account to be created and the provider account is initialized as part of the creation. At least one such service needs to be provided. Currently exactly one of these needs to be `account_aggregation` and `accounts.createAndConfigure` method can be used to create a sub-account under an existing advanced account through this method. Additional `account_management` or `product_management` services may be provided.", "items": {"$ref": "AddAccountService"}, "type": "array"}, "user": {"description": "Optional. Users to be added to the account.", "items": {"$ref": "AddUser"}, "type": "array"}, "users": {"deprecated": true, "description": "Optional. Users to be added to the account. This field is deprecated and will not exist after the API evolves out of beta. Use the `user` field instead.", "items": {"$ref": "CreateUserRequest"}, "type": "array"}}, "type": "object"}, "CreateUserRequest": {"description": "Request message for the `CreateUser` method.", "id": "CreateUserRequest", "properties": {"parent": {"description": "Required. The resource name of the account for which a user will be created. Format: `accounts/{account}`", "type": "string"}, "user": {"$ref": "User", "description": "Optional. The user to create."}, "userId": {"description": "Required. The email address of the user (for example, `<EMAIL>`).", "type": "string"}}, "type": "object"}, "CustomerService": {"description": "Customer service information.", "id": "CustomerService", "properties": {"email": {"description": "Optional. The email address where customer service may be reached.", "type": "string"}, "phone": {"$ref": "PhoneNumber", "description": "Optional. The phone number where customer service may be called."}, "uri": {"description": "Optional. The URI where customer service may be found.", "type": "string"}}, "type": "object"}, "CutoffConfig": {"description": "Configs related to local delivery ends for the day.", "id": "CutoffConfig", "properties": {"localCutoffTime": {"$ref": "LocalCutoffTime", "description": "Time that local delivery ends for the day."}, "noDeliveryPostCutoff": {"description": "Businesses can opt-out of showing n+1 day local delivery when they have a shipping service configured to n day local delivery. For example, if the shipping service defines same-day delivery, and it's past the cut-off, setting this field to `true` results in the calculated shipping service rate returning `NO_DELIVERY_POST_CUTOFF`. In the same example, setting this field to `false` results in the calculated shipping time being one day. This is only for local delivery.", "type": "boolean"}, "storeCloseOffsetHours": {"description": "Only valid with local delivery fulfillment. Represents cutoff time as the number of hours before store closing. Mutually exclusive with `local_cutoff_time`.", "format": "int64", "type": "string"}}, "type": "object"}, "CutoffTime": {"description": "Business days cutoff time definition.", "id": "CutoffTime", "properties": {"hour": {"description": "Required. Hour of the cutoff time until which an order has to be placed to be processed in the same day.", "format": "int32", "type": "integer"}, "minute": {"description": "Required. Minute of the cutoff time until which an order has to be placed to be processed in the same day.", "format": "int32", "type": "integer"}, "timeZone": {"description": "Required. [Timezone identifier](https://developers.google.com/adwords/api/docs/appendix/codes-formats#timezone-ids) For example \"Europe/Zurich\".", "type": "string"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "DeliveryTime": {"description": "Time spent in various aspects from order to the delivery of the product.", "id": "DeliveryTime", "properties": {"cutoffTime": {"$ref": "CutoffTime", "description": "Business days cutoff time definition. If not configured the cutoff time will be defaulted to 8AM PST."}, "handlingBusinessDayConfig": {"$ref": "BusinessDayConfig", "description": "The business days during which orders can be handled. If not provided, Monday to Friday business days will be assumed."}, "maxHandlingDays": {"description": "Maximum number of business days spent before an order is shipped. 0 means same day shipped, 1 means next day shipped. Must be greater than or equal to `min_handling_days`. 'min_handling_days' and 'max_handling_days' should be either set or not set at the same time.", "format": "int32", "type": "integer"}, "maxTransitDays": {"description": "Maximum number of business days that is spent in transit. 0 means same day delivery, 1 means next day delivery. Must be greater than or equal to `min_transit_days`.", "format": "int32", "type": "integer"}, "minHandlingDays": {"description": "Minimum number of business days spent before an order is shipped. 0 means same day shipped, 1 means next day shipped. 'min_handling_days' and 'max_handling_days' should be either set or not set at the same time.", "format": "int32", "type": "integer"}, "minTransitDays": {"description": "Minimum number of business days that is spent in transit. 0 means same day delivery, 1 means next day delivery. Either `min_transit_days`, `max_transit_days` or `transit_time_table` must be set, but not both.", "format": "int32", "type": "integer"}, "transitBusinessDayConfig": {"$ref": "BusinessDayConfig", "description": "The business days during which orders can be in-transit. If not provided, Monday to Friday business days will be assumed."}, "transitTimeTable": {"$ref": "TransitTable", "description": "Transit time table, number of business days spent in transit based on row and column dimensions. Either `min_transit_days`, `max_transit_days` or `transit_time_table` can be set, but not both."}, "warehouseBasedDeliveryTimes": {"description": "Optional. Indicates that the delivery time should be calculated per warehouse (shipping origin location) based on the settings of the selected carrier. When set, no other transit time related field in delivery time should be set.", "items": {"$ref": "WarehouseBasedDeliveryTime"}, "type": "array"}}, "type": "object"}, "DisableProgramRequest": {"description": "Request message for the DisableProgram method.", "id": "DisableProgramRequest", "properties": {}, "type": "object"}, "Distance": {"description": "Maximum delivery radius. This is only required for the local delivery shipment type.", "id": "Distance", "properties": {"unit": {"description": "Unit can differ based on country, it is parameterized to include miles and kilometers.", "enum": ["UNIT_UNSPECIFIED", "MILES", "KILOMETERS"], "enumDescriptions": ["Unit unspecified", "Unit in miles", "Unit in kilometers"], "type": "string"}, "value": {"description": "Integer value of distance.", "format": "int64", "type": "string"}}, "type": "object"}, "EmailPreferences": {"description": "The categories of notifications the user opted into / opted out of. The email preferences do not include mandatory announcements as users can't opt out of them.", "id": "EmailPreferences", "properties": {"name": {"description": "Identifier. The name of the EmailPreferences. The endpoint is only supported for the authenticated user.", "type": "string"}, "newsAndTips": {"description": "Optional. Updates on new features, tips and best practices.", "enum": ["OPT_IN_STATE_UNSPECIFIED", "OPTED_OUT", "OPTED_IN", "UNCONFIRMED"], "enumDescriptions": ["Opt-in status is not specified.", "User has opted out of receiving this type of email.", "User has opted in to receiving this type of email.", "User has opted in to receiving this type of email and the confirmation email has been sent, but user has not yet confirmed the opt in (applies only to certain countries)."], "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EnableProgramRequest": {"description": "Request message for the EnableProgram method.", "id": "EnableProgramRequest", "properties": {}, "type": "object"}, "FindLfpProvidersResponse": {"description": "Response message for the FindLfpProviders method.", "id": "FindLfpProvidersResponse", "properties": {"lfpProviders": {"description": "The LFP providers from the specified merchant in the specified country.", "items": {"$ref": "LfpProvider"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GbpAccount": {"description": "Collection of information related to a Google Business Profile (GBP) account.", "id": "GbpAccount", "properties": {"gbpAccountId": {"description": "The id of the GBP account.", "type": "string"}, "gbpAccountName": {"description": "The name of the Business Profile. For personal accounts: Email id of the owner. For Business accounts: Name of the Business Account.", "type": "string"}, "listingCount": {"description": "Number of listings under this account.", "format": "int64", "type": "string"}, "name": {"description": "Identifier. The resource name of the GBP account. Format: `accounts/{account}/gbpAccount/{gbp_account}`", "type": "string"}, "type": {"description": "The type of the Business Profile.", "enum": ["TYPE_UNSPECIFIED", "USER", "BUSINESS_ACCOUNT"], "enumDescriptions": ["Default value. This value is unused.", "The GBP account is a user account.", "The GBP account is a business account."], "type": "string"}}, "type": "object"}, "GeoTargetArea": {"description": "A list of geotargets that defines the region area.", "id": "GeoTargetArea", "properties": {"geotargetCriteriaIds": {"description": "Required. A non-empty list of [location IDs](https://developers.google.com/adwords/api/docs/appendix/geotargeting). They must all be of the same location type (for example, state).", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "Handshake": {"description": "The current status of establishing of the service. (for example, pending approval or approved).", "id": "Handshake", "properties": {"actor": {"description": "Output only. The most recent account to modify the account service's `approval_status`.", "enum": ["ACTOR_UNSPECIFIED", "ACCOUNT", "OTHER_PARTY"], "enumDescriptions": ["Unspecified actor.", "The last change was done by the account who has this service.", "The last change was done by the other party who this service points to."], "readOnly": true, "type": "string"}, "approvalState": {"description": "Output only. The approval state of this handshake.", "enum": ["APPROVAL_STATE_UNSPECIFIED", "PENDING", "ESTABLISHED", "REJECTED"], "enumDescriptions": ["Unspecified approval status.", "The service was proposed and is waiting to be confirmed.", "Both parties have confirmed the service.", "The service proposal was rejected."], "readOnly": true, "type": "string"}}, "type": "object"}, "Headers": {"description": "A non-empty list of row or column headers for a table. Exactly one of `prices`, `weights`, `num_items`, `postal_code_group_names`, or `location` must be set.", "id": "Headers", "properties": {"locations": {"description": "Required. A list of location ID sets. Must be non-empty. Can only be set if all other fields are not set.", "items": {"$ref": "LocationIdSet"}, "type": "array"}, "numberOfItems": {"description": "Required. A list of inclusive number of items upper bounds. The last value can be `\"infinity\"`. For example `[\"10\", \"50\", \"infinity\"]` represents the headers \"<= 10 items\", \"<= 50 items\", and \"> 50 items\". Must be non-empty. Can only be set if all other fields are not set.", "items": {"type": "string"}, "type": "array"}, "postalCodeGroupNames": {"description": "Required. A list of postal group names. The last value can be `\"all other locations\"`. Example: `[\"zone 1\", \"zone 2\", \"all other locations\"]`. The referred postal code groups must match the delivery country of the service. Must be non-empty. Can only be set if all other fields are not set.", "items": {"type": "string"}, "type": "array"}, "prices": {"description": "Required. A list of inclusive order price upper bounds. The last price's value can be infinity by setting price amount_micros = -1. For example `[{\"amount_micros\": 10000000, \"currency_code\": \"USD\"}, {\"amount_micros\": 500000000, \"currency_code\": \"USD\"}, {\"amount_micros\": -1, \"currency_code\": \"USD\"}]` represents the headers \"<= $10\", \"<= $500\", and \"> $500\". All prices within a service must have the same currency. Must be non-empty. Must be positive except -1. Can only be set if all other fields are not set.", "items": {"$ref": "Price"}, "type": "array"}, "weights": {"description": "Required. A list of inclusive order weight upper bounds. The last weight's value can be infinity by setting price amount_micros = -1. For example `[{\"amount_micros\": 10000000, \"unit\": \"kg\"}, {\"amount_micros\": 50000000, \"unit\": \"kg\"}, {\"amount_micros\": -1, \"unit\": \"kg\"}]` represents the headers \"<= 10kg\", \"<= 50kg\", and \"> 50kg\". All weights within a service must have the same unit. Must be non-empty. Must be positive except -1. Can only be set if all other fields are not set.", "items": {"$ref": "Weight"}, "type": "array"}}, "type": "object"}, "Homepage": {"description": "The `Homepage` message represents a business's store homepage within the system. A business's homepage is the primary domain where customers interact with their store. The homepage can be claimed and verified as a proof of ownership and allows the business to unlock features that require a verified website. For more information, see [Understanding online store URL verification](//support.google.com/merchants/answer/176793).", "id": "Homepage", "properties": {"claimed": {"description": "Output only. Whether the homepage is claimed. See https://support.google.com/merchants/answer/176793.", "readOnly": true, "type": "boolean"}, "name": {"description": "Identifier. The resource name of the store's homepage. Format: `accounts/{account}/homepage`", "type": "string"}, "uri": {"description": "Required. The URI (typically a URL) of the store's homepage.", "type": "string"}}, "type": "object"}, "IdentityAttribute": {"description": "All information related to an identity attribute.", "id": "IdentityAttribute", "properties": {"identityDeclaration": {"description": "Required. The declaration of identity for this attribute.", "enum": ["IDENTITY_DECLARATION_UNSPECIFIED", "SELF_IDENTIFIES_AS", "DOES_NOT_SELF_IDENTIFY_AS"], "enumDescriptions": ["Default value indicating that no selection was made.", "Indicates that the account identifies with the attribute.", "Indicates that the account does not identify with the attribute."], "type": "string"}}, "type": "object"}, "ImageImprovementsAccountLevelSettings": {"description": "Settings for the Automatic Image Improvements.", "id": "ImageImprovementsAccountLevelSettings", "properties": {"allowAutomaticImageImprovements": {"description": "Enables automatic image improvements.", "type": "boolean"}}, "type": "object"}, "Impact": {"description": "The impact of the issue on a region.", "id": "Impact", "properties": {"regionCode": {"description": "The [CLDR region code](https://cldr.unicode.org/) where this issue applies.", "type": "string"}, "severity": {"description": "The severity of the issue on the destination and region.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "ERROR", "SUGGESTION"], "enumDescriptions": ["The severity is unknown.", "The issue causes offers to not serve.", "The issue might affect offers (in the future) or might be an indicator of issues with offers.", "The issue is a suggestion for improvement."], "type": "string"}}, "type": "object"}, "ImpactedDestination": {"description": "The impact of the issue on a destination.", "id": "ImpactedDestination", "properties": {"impacts": {"description": "The (negative) impact for various regions on the given destination.", "items": {"$ref": "Impact"}, "type": "array"}, "reportingContext": {"description": "The impacted reporting context.", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/********).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}}, "type": "object"}, "InStock": {"description": "Collection of information related to InStock.", "id": "InStock", "properties": {"state": {"description": "Output only. The state of the in-stock serving.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "FAILED", "RUNNING", "ACTION_REQUIRED"], "enumDescriptions": ["Default value. This value is unused.", "The review process has concluded successfully. The reviewed item is active.", "The review process failed.", "The review process is running.", "The review process is waiting for the merchant to take action."], "readOnly": true, "type": "string"}, "uri": {"description": "Optional. Product landing page URI. It is only used for the review of MHLSF in-stock serving. This URI domain should match with the business's homepage. Required to be empty if the lsf_type is GHLSF, and required when the lsf_type is MHLSF_FULL or MHLSF_BASIC.", "type": "string"}}, "type": "object"}, "InventoryVerification": {"description": "Collection of information related to [inventory verification](https://support.google.com/merchants/answer/14684499?hl=en&ref_topic=********&sjid=6892280366904591178-NC).", "id": "InventoryVerification", "properties": {"contact": {"description": "Required. The name of the contact for the inventory verification process.", "type": "string"}, "contactEmail": {"description": "Required. The email address of the contact for the inventory verification process.", "type": "string"}, "contactState": {"description": "Output only. The state of the contact verification.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "FAILED", "RUNNING", "ACTION_REQUIRED"], "enumDescriptions": ["Default value. This value is unused.", "The review process has concluded successfully. The reviewed item is active.", "The review process failed.", "The review process is running.", "The review process is waiting for the merchant to take action."], "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the inventory verification process.", "enum": ["STATE_UNSPECIFIED", "ACTION_REQUIRED", "INACTIVE", "RUNNING", "SUCCEEDED", "SUSPENDED"], "enumDescriptions": ["Default value. This value is unused.", "When the merchant needs to initiate the inventory verification process. The next state is INACTIVE.", "When the merchant is ready to request inventory verification.", "The inventory verification process is running. If the merchant is rejected, the next state is INACTIVE.", "The inventory verification process succeeded.", "When merchant fails the inventory verification process and all attempts are exhausted."], "readOnly": true, "type": "string"}}, "type": "object"}, "ItemUpdatesAccountLevelSettings": {"description": "Settings for the Automatic Item Updates.", "id": "ItemUpdatesAccountLevelSettings", "properties": {"allowAvailabilityUpdates": {"description": "If availability updates are enabled, any previous availability values get overwritten if Google finds an out-of-stock annotation on the offer's page. If additionally `allow_strict_availability_updates` field is set to true, values get overwritten if Google finds an in-stock annotation on the offer’s page.", "type": "boolean"}, "allowConditionUpdates": {"description": "If condition updates are enabled, Google always updates item condition with the condition detected from the details of your product.", "type": "boolean"}, "allowPriceUpdates": {"description": "If price updates are enabled, Google always updates the active price with the crawled information.", "type": "boolean"}, "allowStrictAvailabilityUpdates": {"description": "If `allow_availability_updates` is enabled, items are automatically updated in all your Shopping target countries. By default, availability updates will only be applied to items that are 'out of stock' on your website but 'in stock' on Shopping. Set this to true to also update items that are 'in stock' on your website, but 'out of stock' on Google Shopping. In order for this field to have an effect, you must also set `allow_availability_updates`.", "type": "boolean"}}, "type": "object"}, "LfpLink": {"description": "Collection of information related to the LFP link.", "id": "LfpLink", "properties": {"externalAccountId": {"description": "Required. The account ID by which this merchant is known to the LFP provider.", "type": "string"}, "lfpProvider": {"description": "Required. The resource name of the LFP provider. Format: `lfpProviders/{lfp_provider}`", "type": "string"}, "state": {"description": "Output only. The state of the LFP link.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "FAILED", "RUNNING", "ACTION_REQUIRED"], "enumDescriptions": ["Default value. This value is unused.", "The review process has concluded successfully. The reviewed item is active.", "The review process failed.", "The review process is running.", "The review process is waiting for the merchant to take action."], "readOnly": true, "type": "string"}}, "type": "object"}, "LfpProvider": {"description": "Collection of information related to a Local Feed Partnership (LFP) provider.", "id": "LfpProvider", "properties": {"displayName": {"description": "The display name of the LFP provider.", "type": "string"}, "name": {"description": "Identifier. The resource name of the LFP provider. Format: `accounts/{account}/omnichannelSettings/{omnichannel_setting}/lfpProviders/{lfp_provider}`", "type": "string"}, "regionCode": {"description": "Output only. Region code defined by [CLDR](https://cldr.unicode.org/).", "readOnly": true, "type": "string"}}, "type": "object"}, "LinkGbpAccountRequest": {"description": "Request message for the LinkGbpAccount method.", "id": "LinkGbpAccountRequest", "properties": {"gbpEmail": {"description": "Required. The email address of the Business Profile account.", "type": "string"}}, "type": "object"}, "LinkGbpAccountResponse": {"description": "Response message for the LinkGbpAccount method.", "id": "LinkGbpAccountResponse", "properties": {"response": {"$ref": "Empty", "description": "Empty response."}}, "type": "object"}, "LinkLfpProviderRequest": {"description": "Request message for the LinkLfpProvider method.", "id": "LinkLfpProviderRequest", "properties": {"externalAccountId": {"description": "Required. The external account ID by which this merchant is known to the LFP provider.", "type": "string"}}, "type": "object"}, "LinkLfpProviderResponse": {"description": "Response message for the LinkLfpProvider method.", "id": "LinkLfpProviderResponse", "properties": {"response": {"$ref": "Empty", "description": "Empty response."}}, "type": "object"}, "ListAccountIssuesResponse": {"description": "Response message for the `ListAccountIssues` method.", "id": "ListAccountIssuesResponse", "properties": {"accountIssues": {"description": "The issues from the specified account.", "items": {"$ref": "Account<PERSON>ssue"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListAccountRelationshipsResponse": {"description": "Response after trying to list account relationships.", "id": "ListAccountRelationshipsResponse", "properties": {"accountRelationships": {"description": "The account relationships that match your filter.", "items": {"$ref": "AccountRelationship"}, "type": "array"}, "nextPageToken": {"description": "A page token. You can send the `page_token` to get the next page. Only included in the `list` response if there are more pages.", "type": "string"}}, "type": "object"}, "ListAccountServicesResponse": {"description": "Response after trying to list account services.", "id": "ListAccountServicesResponse", "properties": {"accountServices": {"description": "The account services that match your filter.", "items": {"$ref": "AccountService"}, "type": "array"}, "nextPageToken": {"description": "A page token. You can send the `page_token` to get the next page. Only included in the `list` response if there are more pages.", "type": "string"}}, "type": "object"}, "ListAccountsResponse": {"description": "Response message for the `accounts.list` method.", "id": "ListAccountsResponse", "properties": {"accounts": {"description": "The accounts matching the `ListAccountsRequest`.", "items": {"$ref": "Account"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListGbpAccountsResponse": {"description": "Response message for the ListGbpAccounts method.", "id": "ListGbpAccountsResponse", "properties": {"gbpAccounts": {"description": "The GBP accounts from the specified merchant in the specified country.", "items": {"$ref": "GbpAccount"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListOmnichannelSettingsResponse": {"description": "Response message for the ListOmnichannelSettings method.", "id": "ListOmnichannelSettingsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "omnichannelSettings": {"description": "The omnichannel settings from the specified merchant.", "items": {"$ref": "OmnichannelSetting"}, "type": "array"}}, "type": "object"}, "ListOnlineReturnPoliciesResponse": {"description": "Response message for the `ListOnlineReturnPolicies` method.", "id": "ListOnlineReturnPoliciesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `pageToken` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "onlineReturnPolicies": {"description": "The retrieved return policies.", "items": {"$ref": "OnlineReturnPolicy"}, "type": "array"}}, "type": "object"}, "ListProgramsResponse": {"description": "Response message for the ListPrograms method.", "id": "ListProgramsResponse", "properties": {"nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "programs": {"description": "The programs for the given account.", "items": {"$ref": "Program"}, "type": "array"}}, "type": "object"}, "ListRegionsResponse": {"description": "Response message for the `ListRegions` method.", "id": "ListRegionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "regions": {"description": "The regions from the specified business.", "items": {"$ref": "Region"}, "type": "array"}}, "type": "object"}, "ListSubAccountsResponse": {"description": "Response message for the `ListSubAccounts` method.", "id": "ListSubAccountsResponse", "properties": {"accounts": {"description": "The accounts for which the given parent account is an aggregator.", "items": {"$ref": "Account"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListUsersResponse": {"description": "Response message for the `ListUsers` method.", "id": "ListUsersResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "users": {"description": "The users from the specified account.", "items": {"$ref": "User"}, "type": "array"}}, "type": "object"}, "LocalCutoffTime": {"description": "Time that local delivery ends for the day.", "id": "LocalCutoffTime", "properties": {"hour": {"description": "Hour local delivery orders must be placed by to process the same day.", "format": "int64", "type": "string"}, "minute": {"description": "Minute local delivery orders must be placed by to process the same day.", "format": "int64", "type": "string"}}, "type": "object"}, "LocalListingManagement": {"description": "`LocalListingManagement` payload.", "id": "LocalListingManagement", "properties": {}, "type": "object"}, "LocationIdSet": {"description": "A list of location ID sets. Must be non-empty. Can only be set if all other fields are not set.", "id": "LocationIdSet", "properties": {"locationIds": {"description": "Required. A non-empty list of [location IDs](https://developers.google.com/adwords/api/docs/appendix/geotargeting). They must all be of the same location type (For example, state).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "LoyaltyProgram": {"description": "[Loyalty program](https://support.google.com/merchants/answer/12922446) provided by a business.", "id": "LoyaltyProgram", "properties": {"loyaltyProgramTiers": {"description": "Optional. Loyalty program tier of this shipping service.", "items": {"$ref": "LoyaltyProgramTiers"}, "type": "array"}, "programLabel": {"description": "This is the loyalty program label set in your loyalty program settings in Merchant Center. This sub-attribute allows Google to map your loyalty program to eligible offers.", "type": "string"}}, "type": "object"}, "LoyaltyProgramTiers": {"description": "Subset of a business's loyalty program.", "id": "LoyaltyProgramTiers", "properties": {"tierLabel": {"description": "The tier label [tier_label] sub-attribute differentiates offer level benefits between each tier. This value is also set in your program settings in Merchant Center, and is required for data source changes even if your loyalty program only has 1 tier.", "type": "string"}}, "type": "object"}, "MinimumOrderValueTable": {"description": "Table of per store minimum order values for the pickup fulfillment type.", "id": "MinimumOrderValueTable", "properties": {"storeCodeSetWithMovs": {"description": "Required. A list of store code sets sharing the same minimum order value (MOV). At least two sets are required and the last one must be empty, which signifies 'MOV for all other stores'. Each store code can only appear once across all the sets. All prices within a service must have the same currency.", "items": {"$ref": "StoreCodeSetWithMov"}, "type": "array"}}, "type": "object"}, "OmnichannelSetting": {"description": "Collection of information related to the omnichannel settings of a merchant.", "id": "OmnichannelSetting", "properties": {"about": {"$ref": "About", "description": "Optional. The about page URI and state for this country."}, "inStock": {"$ref": "InStock", "description": "Optional. The InStock URI and state for this country."}, "inventoryVerification": {"$ref": "InventoryVerification", "description": "Optional. The inventory verification contact and state for this country."}, "lfpLink": {"$ref": "LfpLink", "description": "Output only. The established link to a LFP provider.", "readOnly": true}, "lsfType": {"description": "Required. The Local Store Front type for this country.", "enum": ["LSF_TYPE_UNSPECIFIED", "GHLSF", "MHLSF_BASIC", "MHLSF_FULL"], "enumDescriptions": ["Default value. This value is unused.", "Google-Hosted Local Store Front. Check the [HC article](https://support.google.com/merchants/answer/********) for more details.", "Merchant-Hosted Local Store Front Basic. Check the [HC article](https://support.google.com/merchants/answer/********) for more details.", "Merchant-Hosted Local Store Front Full. Check the [HC article](https://support.google.com/merchants/answer/********) for more details."], "type": "string"}, "name": {"description": "Identifier. The resource name of the omnichannel setting. Format: `accounts/{account}/omnichannelSettings/{omnichannel_setting}`", "type": "string"}, "odo": {"$ref": "OnDisplayToOrder", "description": "Optional. The On Display to Order (ODO) policy URI and state for this country."}, "pickup": {"$ref": "Pickup", "description": "Optional. The Pickup URI and state for this country."}, "regionCode": {"description": "Required. Immutable. Region code defined by [CLDR](https://cldr.unicode.org/). Must be provided in the Create method, and is immutable.", "type": "string"}}, "type": "object"}, "OnDisplayToOrder": {"description": "Collection of information related to the on display to order ([ODO](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********&sjid=6892280366904591178-NC)).", "id": "OnDisplayToOrder", "properties": {"state": {"description": "Output only. The state of the URI.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "FAILED", "RUNNING", "ACTION_REQUIRED"], "enumDescriptions": ["Default value. This value is unused.", "The review process has concluded successfully. The reviewed item is active.", "The review process failed.", "The review process is running.", "The review process is waiting for the merchant to take action."], "readOnly": true, "type": "string"}, "uri": {"description": "Required. The on display to order (ODO) policy URI.", "type": "string"}}, "type": "object"}, "OnlineReturnPolicy": {"description": "[Online return policy](https://support.google.com/merchants/answer/10220642) object. This is currently used to represent return policies for ads and free listings programs.", "id": "OnlineReturnPolicy", "properties": {"acceptDefectiveOnly": {"description": "Optional. This field specifies if business only accepts defective products for returns.", "type": "boolean"}, "acceptExchange": {"description": "Optional. This field specifies if business allows customers to exchange products.", "type": "boolean"}, "countries": {"description": "Required. Immutable. The countries of sale where the return policy applies. The values must be a valid 2 letter ISO 3166 code.", "items": {"type": "string"}, "type": "array"}, "itemConditions": {"description": "Optional. The item conditions accepted for returns must not be empty unless the type of return policy is 'noReturns'.", "items": {"enum": ["ITEM_CONDITION_UNSPECIFIED", "NEW", "USED"], "enumDescriptions": ["Default value. This value is unused.", "New.", "Used."], "type": "string"}, "type": "array"}, "label": {"description": "Required. Immutable. This field represents the unique user-defined label of the return policy. It is important to note that the same label cannot be used in different return policies for the same country. Unless a product specifies a specific label attribute, policies will be automatically labeled as 'default'. To assign a custom return policy to certain product groups, follow the instructions provided in the [Return policy label] (https://support.google.com/merchants/answer/9445425). The label can contain up to 50 characters.", "type": "string"}, "name": {"description": "Identifier. The name of the `OnlineReturnPolicy` resource. Format: `accounts/{account}/onlineReturnPolicies/{return_policy}`", "type": "string"}, "policy": {"$ref": "Policy", "description": "Optional. The return policy."}, "processRefundDays": {"description": "Optional. The field specifies the number of days it takes for business to process refunds.", "format": "int32", "type": "integer"}, "restockingFee": {"$ref": "RestockingFee", "description": "Optional. The restocking fee that applies to all return reason categories. This would be treated as a free restocking fee if the value is not set."}, "returnLabelSource": {"description": "Optional. The field specifies the return label source.", "enum": ["RETURN_LABEL_SOURCE_UNSPECIFIED", "DOWNLOAD_AND_PRINT", "IN_THE_PACKAGE", "CUSTOMER_RESPONSIBILITY"], "enumDescriptions": ["Default value. This value is unused.", "Download and print.", "Label include in the package.", "Customer to provide."], "type": "string"}, "returnMethods": {"description": "Optional. The return methods of how customers can return an item. This value is required to not be empty unless the type of return policy is noReturns.", "items": {"enum": ["RETURN_METHOD_UNSPECIFIED", "BY_MAIL", "IN_STORE", "AT_A_KIOSK"], "enumDescriptions": ["Default value. This value is unused.", "Return by mail.", "Return in store.", "Return at a kiosk."], "type": "string"}, "type": "array"}, "returnPolicyId": {"description": "Output only. Return policy ID generated by Google.", "readOnly": true, "type": "string"}, "returnPolicyUri": {"description": "Required. The return policy uri. This can used by Google to do a sanity check for the policy. It must be a valid URL.", "type": "string"}, "returnShippingFee": {"$ref": "ReturnShippingFee", "description": "Optional. The return shipping fee. Should be set only when customer need to download and print the return label."}, "seasonalOverrides": {"description": "Optional. Overrides to the general policy for orders placed during a specific set of time intervals.", "items": {"$ref": "SeasonalOverride"}, "type": "array"}}, "type": "object"}, "PhoneNumber": {"description": "An object representing a phone number, suitable as an API wire format. This representation: - should not be used for locale-specific formatting of a phone number, such as \"+**************** ext. 123\" - is not designed for efficient storage - may not be suitable for dialing - specialized libraries (see references) should be used to parse the number for that purpose To do something meaningful with this number, such as format it for various use-cases, convert it to an `i18n.phonenumbers.PhoneNumber` object first. For instance, in Java this would be: com.google.type.PhoneNumber wireProto = com.google.type.PhoneNumber.newBuilder().build(); com.google.i18n.phonenumbers.Phonenumber.PhoneNumber phoneNumber = PhoneNumberUtil.getInstance().parse(wireProto.getE164Number(), \"ZZ\"); if (!wireProto.getExtension().isEmpty()) { phoneNumber.setExtension(wireProto.getExtension()); } Reference(s): - https://github.com/google/libphonenumber", "id": "PhoneNumber", "properties": {"e164Number": {"description": "The phone number, represented as a leading plus sign ('+'), followed by a phone number that uses a relaxed ITU E.164 format consisting of the country calling code (1 to 3 digits) and the subscriber number, with no additional spaces or formatting. For example: - correct: \"+15552220123\" - incorrect: \"+1 (555) 222-01234 x123\" The ITU E.164 format limits the latter to 12 digits, but in practice not all countries respect that, so we relax that restriction here. National-only numbers are not allowed. References: - https://www.itu.int/rec/T-REC-E.164-201011-I - https://en.wikipedia.org/wiki/E.164. - https://en.wikipedia.org/wiki/List_of_country_calling_codes", "type": "string"}, "extension": {"description": "The phone number's extension. The extension is not standardized in ITU recommendations, except for being defined as a series of numbers with a maximum length of 40 digits. Other than digits, some other dialing characters such as ',' (indicating a wait) or '#' may be stored here. Note that no regions currently use extensions with short codes, so this field is normally only set in conjunction with an E.164 number. It is held separately from the E.164 number to allow for short code extensions in the future.", "type": "string"}, "shortCode": {"$ref": "ShortCode", "description": "A short code. Reference(s): - https://wikipedia.org/wiki/Short_code"}}, "type": "object"}, "Pickup": {"description": "Collection of information related to <PERSON><PERSON>.", "id": "Pickup", "properties": {"state": {"description": "Output only. The state of the pickup serving.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "FAILED", "RUNNING", "ACTION_REQUIRED"], "enumDescriptions": ["Default value. This value is unused.", "The review process has concluded successfully. The reviewed item is active.", "The review process failed.", "The review process is running.", "The review process is waiting for the merchant to take action."], "readOnly": true, "type": "string"}, "uri": {"description": "Required. Pickup product page URI. It is only used for the review of pickup serving. This URI domain should match with the business's homepage.", "type": "string"}}, "type": "object"}, "Policy": {"description": "The available policies.", "id": "Policy", "properties": {"days": {"description": "The number of days items can be returned after delivery, where one day is defined as 24 hours after the delivery timestamp. Required for `NUMBER_OF_DAYS_AFTER_DELIVERY` returns.", "format": "int64", "type": "string"}, "type": {"description": "Policy type.", "enum": ["TYPE_UNSPECIFIED", "NUMBER_OF_DAYS_AFTER_DELIVERY", "NO_RETURNS", "LIFETIME_RETURNS"], "enumDescriptions": ["Default value. This value is unused.", "The number of days within which a return is valid after delivery.", "No returns.", "Life time returns."], "type": "string"}}, "type": "object"}, "PostalAddress": {"description": "Represents a postal address, such as for postal delivery or payments addresses. With a postal address, a postal service can deliver items to a premise, P.O. box, or similar. A postal address is not intended to model geographical locations like roads, towns, or mountains. In typical usage, an address would be created by user input or from importing existing data, depending on the type of process. Advice on address input or editing: - Use an internationalization-ready address widget such as https://github.com/google/libaddressinput. - Users should not be presented with UI elements for input or editing of fields outside countries where that field is used. For more guidance on how to use this schema, see: https://support.google.com/business/answer/6397478.", "id": "PostalAddress", "properties": {"addressLines": {"description": "Unstructured address lines describing the lower levels of an address. Because values in `address_lines` do not have type information and may sometimes contain multiple values in a single field (for example, \"Austin, TX\"), it is important that the line order is clear. The order of address lines should be \"envelope order\" for the country or region of the address. In places where this can vary (for example, Japan), `address_language` is used to make it explicit (for example, \"ja\" for large-to-small ordering and \"ja-Latn\" or \"en\" for small-to-large). In this way, the most specific line of an address can be selected based on the language. The minimum permitted structural representation of an address consists of a `region_code` with all remaining information placed in the `address_lines`. It would be possible to format such an address very approximately without geocoding, but no semantic reasoning could be made about any of the address components until it was at least partially resolved. Creating an address only containing a `region_code` and `address_lines` and then geocoding is the recommended way to handle completely unstructured addresses (as opposed to guessing which parts of the address should be localities or administrative areas).", "items": {"type": "string"}, "type": "array"}, "administrativeArea": {"description": "Optional. Highest administrative subdivision which is used for postal addresses of a country or region. For example, this can be a state, a province, an oblast, or a prefecture. For Spain, this is the province and not the autonomous community (for example, \"Barcelona\" and not \"Catalonia\"). Many countries don't use an administrative area in postal addresses. For example, in Switzerland, this should be left unpopulated.", "type": "string"}, "languageCode": {"description": "Optional. BCP-47 language code of the contents of this address (if known). This is often the UI language of the input form or is expected to match one of the languages used in the address' country/region, or their transliterated equivalents. This can affect formatting in certain countries, but is not critical to the correctness of the data and will never affect any validation or other non-formatting related operations. If this value is not known, it should be omitted (rather than specifying a possibly incorrect default). Examples: \"zh-Hant\", \"ja\", \"ja-Latn\", \"en\".", "type": "string"}, "locality": {"description": "Optional. Generally refers to the city or town portion of the address. Examples: US city, IT comune, UK post town. In regions of the world where localities are not well defined or do not fit into this structure well, leave `locality` empty and use `address_lines`.", "type": "string"}, "organization": {"description": "Optional. The name of the organization at the address.", "type": "string"}, "postalCode": {"description": "Optional. Postal code of the address. Not all countries use or require postal codes to be present, but where they are used, they may trigger additional validation with other parts of the address (for example, state or zip code validation in the United States).", "type": "string"}, "recipients": {"description": "Optional. The recipient at the address. This field may, under certain circumstances, contain multiline information. For example, it might contain \"care of\" information.", "items": {"type": "string"}, "type": "array"}, "regionCode": {"description": "Required. CLDR region code of the country/region of the address. This is never inferred and it is up to the user to ensure the value is correct. See https://cldr.unicode.org/ and https://www.unicode.org/cldr/charts/30/supplemental/territory_information.html for details. Example: \"CH\" for Switzerland.", "type": "string"}, "revision": {"description": "The schema revision of the `PostalAddress`. This must be set to 0, which is the latest revision. All new revisions **must** be backward compatible with old revisions.", "format": "int32", "type": "integer"}, "sortingCode": {"description": "Optional. Additional, country-specific, sorting code. This is not used in most regions. Where it is used, the value is either a string like \"CEDEX\", optionally followed by a number (for example, \"CEDEX 7\"), or just a number alone, representing the \"sector code\" (Jamaica), \"delivery area indicator\" (Malawi) or \"post office indicator\" (Côte d'Ivoire).", "type": "string"}, "sublocality": {"description": "Optional. Sublocality of the address. For example, this can be a neighborhood, borough, or district.", "type": "string"}}, "type": "object"}, "PostalCodeArea": {"description": "A list of postal codes that defines the region area. Note: All regions defined using postal codes are accessible through the account's `ShippingSettings.postalCodeGroups` resource.", "id": "PostalCodeArea", "properties": {"postalCodes": {"description": "Required. A range of postal codes.", "items": {"$ref": "PostalCodeRange"}, "type": "array"}, "regionCode": {"description": "Required. [CLDR territory code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml) or the country the postal code group applies to.", "type": "string"}}, "type": "object"}, "PostalCodeRange": {"description": "A range of postal codes that defines the region area.", "id": "PostalCodeRange", "properties": {"begin": {"description": "Required. A postal code or a pattern of the form prefix* denoting the inclusive lower bound of the range defining the area. Examples values: `94108`, `9410*`, `9*`.", "type": "string"}, "end": {"description": "Optional. A postal code or a pattern of the form `prefix*` denoting the inclusive upper bound of the range defining the area. It must have the same length as postalCodeRangeBegin: if postalCodeRangeBegin is a postal code then postalCodeRangeEnd must be a postal code too; if postalCodeRangeBegin is a pattern then postalCodeRangeEnd must be a pattern with the same prefix length. Optional: if not set, then the area is defined as being all the postal codes matching postalCodeRangeBegin.", "type": "string"}}, "type": "object"}, "Price": {"description": "The price represented as a number and currency.", "id": "Price", "properties": {"amountMicros": {"description": "The price represented as a number in micros (1 million micros is an equivalent to one's currency standard unit, for example, 1 USD = 1000000 micros).", "format": "int64", "type": "string"}, "currencyCode": {"description": "The currency of the price using three-letter acronyms according to [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217).", "type": "string"}}, "type": "object"}, "ProductChange": {"description": "The change that happened to the product including old value, new value, country code as the region code and reporting context.", "id": "ProductChange", "properties": {"newValue": {"description": "The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "oldValue": {"description": "The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "regionCode": {"description": "Countries that have the change (if applicable). Represented in the ISO 3166 format.", "type": "string"}, "reportingContext": {"description": "Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/********).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}}, "type": "object"}, "ProductStatusChangeMessage": {"description": "The message that the merchant will receive to notify about product status change event", "id": "ProductStatusChangeMessage", "properties": {"account": {"description": "The target account that owns the entity that changed. Format : `accounts/{merchant_id}`", "type": "string"}, "attribute": {"description": "The attribute in the resource that changed, in this case it will be always `Status`.", "enum": ["ATTRIBUTE_UNSPECIFIED", "STATUS"], "enumDescriptions": ["Unspecified attribute", "Status of the changed entity"], "type": "string"}, "changes": {"description": "A message to describe the change that happened to the product", "items": {"$ref": "ProductChange"}, "type": "array"}, "eventTime": {"description": "The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.", "format": "google-datetime", "type": "string"}, "expirationTime": {"description": "Optional. The product expiration time. This field will not be set if the notification is sent for a product deletion event.", "format": "google-datetime", "type": "string"}, "managingAccount": {"description": "The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id}`", "type": "string"}, "resource": {"description": "The product name. Format: `accounts/{account}/products/{product}`", "type": "string"}, "resourceId": {"description": "The product id.", "type": "string"}, "resourceType": {"description": "The resource that changed, in this case it will always be `Product`.", "enum": ["RESOURCE_UNSPECIFIED", "PRODUCT"], "enumDescriptions": ["Unspecified resource", "Resource type : product"], "type": "string"}}, "type": "object"}, "ProductsManagement": {"description": "`ProductsManagement` payload.", "id": "ProductsManagement", "properties": {}, "type": "object"}, "Program": {"description": "Defines participation in a given program for the specified account. Programs provide a mechanism for adding functionality to a Merchant Center accounts. A typical example of this is the [Free product listings](https://support.google.com/merchants/answer/********) program, which enables products from a business's store to be shown across Google for free. The following list is the available set of program resource IDs accessible through the API: * `free-listings` * `shopping-ads` * `youtube-shopping-checkout`", "id": "Program", "properties": {"activeRegionCodes": {"description": "Output only. The regions in which the account is actively participating in the program. Active regions are defined as those where all program requirements affecting the regions have been met. Region codes are defined by [CLDR](https://cldr.unicode.org/). This is either a country where the program applies specifically to that country or `001` when the program applies globally.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "documentationUri": {"description": "Output only. The URL of a Merchant Center help page describing the program.", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The resource name of the program. Format: `accounts/{account}/programs/{program}`", "type": "string"}, "state": {"description": "Output only. The participation state of the account in the program.", "enum": ["STATE_UNSPECIFIED", "NOT_ELIGIBLE", "ELIGIBLE", "ENABLED"], "enumDescriptions": ["Default value. This value is unused.", "The account is not eligible to participate in the program.", "The account is eligible to participate in the program.", "The program is enabled for the account."], "readOnly": true, "type": "string"}, "unmetRequirements": {"description": "Output only. The requirements that the account has not yet satisfied that are affecting participation in the program.", "items": {"$ref": "Requirement"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ProposeAccountServiceRequest": {"description": "Request to propose an account service.", "id": "ProposeAccountServiceRequest", "properties": {"accountService": {"$ref": "AccountService", "description": "Required. The account service to propose."}, "provider": {"description": "Required. The provider of the service. Either the reference to an account such as `providers/123` or a well-known service provider (one of `providers/GOOGLE_ADS` or `providers/GOOGLE_BUSINESS_PROFILE`).", "type": "string"}}, "type": "object"}, "RateGroup": {"description": "Shipping rate group definitions. Only the last one is allowed to have an empty `applicable_shipping_labels`, which means \"everything else\". The other `applicable_shipping_labels` must not overlap.", "id": "RateGroup", "properties": {"applicableShippingLabels": {"description": "Required. A list of [shipping labels](https://support.google.com/merchants/answer/6324504) defining the products to which this rate group applies to. This is a disjunction: only one of the labels has to match for the rate group to apply. May only be empty for the last rate group of a service.", "items": {"type": "string"}, "type": "array"}, "carrierRates": {"description": "Optional. A list of carrier rates that can be referred to by `main_table` or `single_value`.", "items": {"$ref": "CarrierRate"}, "type": "array"}, "mainTable": {"$ref": "Table", "description": "A table defining the rate group, when `single_value` is not expressive enough. Can only be set if `single_value` is not set."}, "name": {"description": "Optional. Name of the rate group. If set has to be unique within shipping service.", "type": "string"}, "singleValue": {"$ref": "Value", "description": "The value of the rate group (For example flat rate $10). Can only be set if `main_table` and `subtables` are not set."}, "subtables": {"description": "Optional. A list of subtables referred to by `main_table`. Can only be set if `main_table` is set.", "items": {"$ref": "Table"}, "type": "array"}}, "type": "object"}, "Region": {"description": "Represents a geographic region that you can use as a target with both the `RegionalInventory` and `ShippingSettings` services. You can define regions as collections of either postal codes or, in some countries, using predefined geotargets. For more information, see [Set up regions ](https://support.google.com/merchants/answer/7410946#zippy=%2Ccreate-a-new-region) for more information.", "id": "Region", "properties": {"displayName": {"description": "Optional. The display name of the region.", "type": "string"}, "geotargetArea": {"$ref": "GeoTargetArea", "description": "Optional. A list of geotargets that defines the region area."}, "name": {"description": "Identifier. The resource name of the region. Format: `accounts/{account}/regions/{region}`", "type": "string"}, "postalCodeArea": {"$ref": "PostalCodeArea", "description": "Optional. A list of postal codes that defines the region area."}, "regionalInventoryEligible": {"description": "Output only. Indicates if the region is eligible for use in the Regional Inventory configuration.", "readOnly": true, "type": "boolean"}, "shippingEligible": {"description": "Output only. Indicates if the region is eligible for use in the Shipping Services configuration.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "RejectAccountServiceRequest": {"description": "Request to reject an account service.", "id": "RejectAccountServiceRequest", "properties": {}, "type": "object"}, "RequestInventoryVerificationRequest": {"description": "Request message for the RequestInventoryVerification method.", "id": "RequestInventoryVerificationRequest", "properties": {}, "type": "object"}, "RequestInventoryVerificationResponse": {"description": "Response message for the RequestInventoryVerification method.", "id": "RequestInventoryVerificationResponse", "properties": {"omnichannelSetting": {"$ref": "OmnichannelSetting", "description": "The omnichannel setting that was updated."}}, "type": "object"}, "Required": {"description": "Describes the terms of service which are required to be accepted.", "id": "Required", "properties": {"termsOfService": {"description": "Required. The `TermsOfService` that need to be accepted.", "type": "string"}, "tosFileUri": {"description": "Required. Full URL to the terms of service file. This field is the same as `TermsOfService.file_uri`, it is added here for convenience only.", "type": "string"}}, "type": "object"}, "Requirement": {"description": "Defines a requirement specified for participation in the program.", "id": "Requirement", "properties": {"affectedRegionCodes": {"description": "Output only. The regions that are currently affected by this requirement not being met. Region codes are defined by [CLDR](https://cldr.unicode.org/). This is either a country where the program applies specifically to that country or `001` when the program applies globally.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "documentationUri": {"description": "Output only. The URL of a help page describing the requirement.", "readOnly": true, "type": "string"}, "title": {"description": "Output only. Name of the requirement.", "readOnly": true, "type": "string"}}, "type": "object"}, "RestockingFee": {"description": "The restocking fee. This can be a flat fee or a micro percent.", "id": "RestockingFee", "properties": {"fixedFee": {"$ref": "Price", "description": "Fixed restocking fee."}, "microPercent": {"description": "Percent of total price in micros. 15,000,000 means 15% of the total price would be charged.", "format": "int32", "type": "integer"}}, "type": "object"}, "ReturnShippingFee": {"description": "The return shipping fee. This can either be a fixed fee or a boolean to indicate that the customer pays the actual shipping cost.", "id": "ReturnShippingFee", "properties": {"fixedFee": {"$ref": "Price", "description": "Fixed return shipping fee amount. This value is only applicable when type is `FIXED`. We will treat the return shipping fee as free if type is `FIXED` and this value is not set."}, "type": {"description": "Required. Type of return shipping fee.", "enum": ["TYPE_UNSPECIFIED", "FIXED", "CUSTOMER_PAYING_ACTUAL_FEE"], "enumDescriptions": ["Default value. This value is unused.", "The return shipping fee is a fixed value.", "Customers will pay the actual return shipping fee."], "type": "string"}}, "type": "object"}, "Row": {"description": "Include a list of cells.", "id": "Row", "properties": {"cells": {"description": "Required. The list of cells that constitute the row. Must have the same length as `columnHeaders` for two-dimensional tables, a length of 1 for one-dimensional tables.", "items": {"$ref": "Value"}, "type": "array"}}, "type": "object"}, "SeasonalOverride": {"id": "SeasonalOverride", "properties": {"endDate": {"$ref": "Date", "description": "Required. seasonal override end date (inclusive)."}, "label": {"description": "Required. Display name of this seasonal override in Merchant Center.", "type": "string"}, "returnDays": {"description": "Number of days (from the delivery date) that the product can be returned.", "format": "int32", "type": "integer"}, "returnUntilDate": {"$ref": "Date", "description": "Fixed end date until which the product can be returned."}, "startDate": {"$ref": "Date", "description": "Required. Defines the date range when this seasonal override applies. Both start_date and end_date are inclusive. The dates of the seasonal overrides should not overlap."}}, "type": "object"}, "Service": {"description": "Shipping service.", "id": "Service", "properties": {"active": {"description": "Required. A boolean exposing the active status of the shipping service.", "type": "boolean"}, "currencyCode": {"description": "Required. The CLDR code of the currency to which this service applies. Must match that of the prices in rate groups.", "type": "string"}, "deliveryCountries": {"description": "Required. The CLDR territory code of the countries to which the service applies.", "items": {"type": "string"}, "type": "array"}, "deliveryTime": {"$ref": "DeliveryTime", "description": "Required. Time spent in various aspects from order to the delivery of the product."}, "loyaltyPrograms": {"description": "Optional. Loyalty programs that this shipping service is limited to.", "items": {"$ref": "LoyaltyProgram"}, "type": "array"}, "minimumOrderValue": {"$ref": "Price", "description": "Optional. Minimum order value for this service. If set, indicates that customers will have to spend at least this amount. All prices within a service must have the same currency. Cannot be set together with `minimum_order_value_table`."}, "minimumOrderValueTable": {"$ref": "MinimumOrderValueTable", "description": "Optional. Table of per store minimum order values for the pickup fulfillment type. Cannot be set together with `minimum_order_value`."}, "rateGroups": {"description": "Optional. Shipping rate group definitions. Only the last one is allowed to have an empty `applicable_shipping_labels`, which means \"everything else\". The other `applicable_shipping_labels` must not overlap.", "items": {"$ref": "RateGroup"}, "type": "array"}, "serviceName": {"description": "Required. Free-form name of the service. Must be unique within target account.", "type": "string"}, "shipmentType": {"description": "Optional. Type of locations this service ships orders to.", "enum": ["SHIPMENT_TYPE_UNSPECIFIED", "DELIVERY", "LOCAL_DELIVERY", "COLLECTION_POINT"], "enumDescriptions": ["This service did not specify shipment type.", "This service ships orders to an address chosen by the customer.", "This service ships orders to an address chosen by the customer. The order is shipped from a local store near by.", "This service ships orders to an address chosen by the customer. The order is shipped from a collection point."], "type": "string"}, "storeConfig": {"$ref": "StoreConfig", "description": "A list of stores your products are delivered from. This is only valid for the local delivery shipment type."}}, "type": "object"}, "ShippingSettings": {"description": "The Merchant Center account's [shipping settings](https://support.google.com/merchants/answer/6069284). The `ShippingSettings` resource lets you retrieve and update the shipping settings of your advanced account and all its associated sub-accounts.", "id": "ShippingSettings", "properties": {"etag": {"description": "Required. This field helps avoid async issues. It ensures that the shipping setting data doesn't change between the `get` call and the `insert` call. The user should follow these steps: 1. Set the etag field as an empty string for the initial shipping setting creation. 2. After the initial creation, call the `get` method to obtain an etag and the current shipping setting data before calling `insert`. 3. Modify the shipping setting information. 4. Call the `insert` method with the shipping setting information and the etag obtained in step 2. 5. If the shipping setting data changes between step 2 and step 4, the insert request will fail because the etag changes every time the shipping setting data changes. In this case, the user should repeat steps 2-4 with the new etag.", "type": "string"}, "name": {"description": "Identifier. The resource name of the shipping settings. Format: `accounts/{account}/shippingSettings`. For example, `accounts/123456/shippingSettings`.", "type": "string"}, "services": {"description": "Optional. The target account's list of services.", "items": {"$ref": "Service"}, "type": "array"}, "warehouses": {"description": "Optional. A list of warehouses which can be referred to in `services`.", "items": {"$ref": "Warehouse"}, "type": "array"}}, "type": "object"}, "ShortCode": {"description": "An object representing a short code, which is a phone number that is typically much shorter than regular phone numbers and can be used to address messages in MMS and SMS systems, as well as for abbreviated dialing (For example \"Text 611 to see how many minutes you have remaining on your plan.\"). Short codes are restricted to a region and are not internationally dialable, which means the same short code can exist in different regions, with different usage and pricing, even if those regions share the same country calling code (For example: US and CA).", "id": "ShortCode", "properties": {"number": {"description": "Required. The short code digits, without a leading plus ('+') or country calling code. For example \"611\".", "type": "string"}, "regionCode": {"description": "Required. The BCP-47 region code of the location where calls to this short code can be made, such as \"US\" and \"BB\". Reference(s): - http://www.unicode.org/reports/tr35/#unicode_region_subtag", "type": "string"}}, "type": "object"}, "StoreCodeSetWithMov": {"description": "A list of store code sets sharing the same minimum order value. At least two sets are required and the last one must be empty, which signifies 'MOV for all other stores'. Each store code can only appear once across all the sets. All prices within a service must have the same currency.", "id": "StoreCodeSetWithMov", "properties": {"storeCodes": {"description": "Optional. A list of unique store codes or empty for the catch all.", "items": {"type": "string"}, "type": "array"}, "value": {"$ref": "Price", "description": "The minimum order value for the given stores."}}, "type": "object"}, "StoreConfig": {"description": "A list of stores your products are delivered from. This is only valid for the local delivery shipment type.", "id": "StoreConfig", "properties": {"cutoffConfig": {"$ref": "CutoffConfig", "description": "Configs related to local delivery ends for the day."}, "serviceRadius": {"$ref": "Distance", "description": "Maximum delivery radius. This is only required for the local delivery shipment type."}, "storeCodes": {"description": "Optional. A list of store codes that provide local delivery. If empty, then `all_stores` must be true.", "items": {"type": "string"}, "type": "array"}, "storeServiceType": {"description": "Indicates whether all stores, or selected stores, listed by this business provide local delivery.", "enum": ["STORE_SERVICE_TYPE_UNSPECIFIED", "ALL_STORES", "SELECTED_STORES"], "enumDescriptions": ["Did not specify store service type.", "Indicates whether all stores, current and future, listed by this business provide local delivery.", "Indicates that only the stores listed in `store_codes` are eligible for local delivery."], "type": "string"}}, "type": "object"}, "Table": {"description": "A table defining the rate group, when `single_value` is not expressive enough.", "id": "Table", "properties": {"columnHeaders": {"$ref": "Headers", "description": "Headers of the table's columns. Optional: if not set then the table has only one dimension."}, "name": {"description": "Name of the table. Required for subtables, ignored for the main table.", "type": "string"}, "rowHeaders": {"$ref": "Headers", "description": "Required. Headers of the table's rows."}, "rows": {"description": "Required. The list of rows that constitute the table. Must have the same length as `row_headers`.", "items": {"$ref": "Row"}, "type": "array"}}, "type": "object"}, "TermsOfService": {"description": "The `TermsOfService` message represents a specific version of the terms of service that merchants must accept to access certain features or services. For more information, see [Terms of Service](https://support.google.com/merchants/answer/160173). This message is important for the onboarding process, ensuring that merchants agree to the necessary legal agreements for using the service. Merchants can retrieve the latest terms of service for a given `kind` and `region` through `RetrieveLatestTermsOfService`, and accept them as required through `AcceptTermsOfService`.", "id": "TermsOfService", "properties": {"external": {"description": "Whether this terms of service version is external. External terms of service versions can only be agreed through external processes and not directly by the merchant through UI or API.", "type": "boolean"}, "fileUri": {"description": "URI for terms of service file that needs to be displayed to signing users.", "type": "string"}, "kind": {"description": "The Kind this terms of service version applies to.", "enum": ["TERMS_OF_SERVICE_KIND_UNSPECIFIED", "MERCHANT_CENTER"], "enumDescriptions": ["Default value. This value is unused.", "Merchant Center application."], "type": "string"}, "name": {"description": "Identifier. The resource name of the terms of service version. Format: `termsOfService/{version}`", "type": "string"}, "regionCode": {"description": "Region code as defined by [CLDR](https://cldr.unicode.org/). This is either a country where the ToS applies specifically to that country or `001` when the same `TermsOfService` can be signed in any country. However note that when signing a ToS that applies globally we still expect that a specific country is provided (this should be merchant business country or program country of participation).", "type": "string"}}, "type": "object"}, "TermsOfServiceAgreementState": {"description": "This resource represents the agreement state for a given account and terms of service kind. The state is as follows: * If the business has accepted a terms of service, `accepted` will be populated, otherwise it will be empty * If the business must sign a terms of service, `required` will be populated, otherwise it will be empty. Note that both `required` and `accepted` can be present. In this case the `accepted` terms of services will have an expiration date set in the `valid_until` field. The `required` terms of services need to be accepted before `valid_until` in order for the account to continue having a valid agreement. When accepting new terms of services we expect third-party providers to display the text associated with the given terms of service agreement (the url to the file containing the text is added in the Required message below as `tos_file_uri`). The actual acceptance of the terms of service is done by calling accept on the `TermsOfService` resource. `valid_until` field. The `required` terms of services need to be accepted before `valid_until` in order for the account to continue having a valid agreement. When accepting new terms of services, we expect third-party providers to display the text associated with the given terms of service agreement (the url to the file containing the text is added in the Required message below as `tos_file_uri`. The actual acceptance of the terms of service is done by calling accept on the `TermsOfService` resource.", "id": "TermsOfServiceAgreementState", "properties": {"accepted": {"$ref": "Accepted", "description": "Optional. The accepted terms of service of this kind and for the associated region_code"}, "name": {"description": "Identifier. The resource name of the terms of service version. Format: `accounts/{account}/termsOfServiceAgreementState/{identifier}` The identifier format is: `{TermsOfServiceKind}-{country}` For example, an identifier could be: `MERCHANT_CENTER-EU` or `MERCHANT_CENTER-US`.", "type": "string"}, "regionCode": {"description": "Required. Region code as defined by https://cldr.unicode.org/. This is the country the current state applies to.", "type": "string"}, "required": {"$ref": "Required", "description": "Optional. The required terms of service"}, "termsOfServiceKind": {"description": "Required. Terms of Service kind associated with the particular version.", "enum": ["TERMS_OF_SERVICE_KIND_UNSPECIFIED", "MERCHANT_CENTER"], "enumDescriptions": ["Default value. This value is unused.", "Merchant Center application."], "type": "string"}}, "type": "object"}, "TimeZone": {"description": "Represents a time zone from the [IANA Time Zone Database](https://www.iana.org/time-zones).", "id": "TimeZone", "properties": {"id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "type": "string"}, "version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "type": "string"}}, "type": "object"}, "TransitTable": {"description": "Transit time table, number of business days spent in transit based on row and column dimensions. Either `min_transit_days`, `max_transit_days` or `transit_time_table` can be set, but not both.", "id": "TransitTable", "properties": {"postalCodeGroupNames": {"description": "Required. A list of region names Region.name . The last value can be `\"all other locations\"`. Example: `[\"zone 1\", \"zone 2\", \"all other locations\"]`. The referred postal code groups must match the delivery country of the service.", "items": {"type": "string"}, "type": "array"}, "rows": {"description": "Required. If there's only one dimension set of `postal_code_group_names` or `transit_time_labels`, there are multiple rows each with one value for that dimension. If there are two dimensions, each row corresponds to a `postal_code_group_names`, and columns (values) to a `transit_time_labels`.", "items": {"$ref": "TransitTimeRow"}, "type": "array"}, "transitTimeLabels": {"description": "Required. A list of transit time labels. The last value can be `\"all other labels\"`. Example: `[\"food\", \"electronics\", \"all other labels\"]`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TransitTimeRow": {"description": "If there's only one dimension set of `postal_code_group_names` or `transit_time_labels`, there are multiple rows each with one value for that dimension. If there are two dimensions, each row corresponds to a `postal_code_group_names`, and columns (values) to a `transit_time_labels`.", "id": "TransitTimeRow", "properties": {"values": {"description": "Required. Transit time range (min-max) in business days.", "items": {"$ref": "TransitTimeValue"}, "type": "array"}}, "type": "object"}, "TransitTimeValue": {"description": "Transit time range (min-max) in business days.", "id": "TransitTimeValue", "properties": {"maxTransitDays": {"description": "Must be greater than or equal to `min_transit_days`.", "format": "int32", "type": "integer"}, "minTransitDays": {"description": "Minimum transit time range in business days. 0 means same day delivery, 1 means next day delivery.", "format": "int32", "type": "integer"}}, "type": "object"}, "UnclaimHomepageRequest": {"description": "Request message for the `UnclaimHomepage` method.", "id": "UnclaimHomepageRequest", "properties": {}, "type": "object"}, "UriSettings": {"description": "URL settings for cart or checkout URL.", "id": "UriSettings", "properties": {"cartUriTemplate": {"description": "Cart URL template. When the placeholders are expanded will redirect the buyer to the cart page on the merchant website with the selected item in cart. For more details, check the [help center doc](https://support.google.com/merchants/answer/********#method1&zippy=%2Cproduct-level-url-formatting%2Caccount-level-url-formatting)", "type": "string"}, "checkoutUriTemplate": {"description": "Checkout URL template. When the placeholders are expanded will redirect the buyer to the merchant checkout page with the item in the cart. For more details, check the [help center doc](https://support.google.com/merchants/answer/********#method1&zippy=%2Cproduct-level-url-formatting%2Caccount-level-url-formatting)", "type": "string"}}, "type": "object"}, "User": {"description": "The `User` message represents a user associated with a Merchant Center account. It is used to manage user permissions and access rights within the account. For more information, see [Frequently asked questions about people and access levels](//support.google.com/merchants/answer/********).", "id": "User", "properties": {"accessRights": {"description": "Required. The [access rights](https://support.google.com/merchants/answer/********?sjid=6789834943175119429-EU#accesstypes) the user has.", "items": {"enum": ["ACCESS_RIGHT_UNSPECIFIED", "STANDARD", "READ_ONLY", "ADMIN", "PERFORMANCE_REPORTING"], "enumDescriptions": ["Default value. This value is unused.", "Standard access rights.", "Has access to the same read-only methods as STANDARD, but no access to any mutating methods.", "Admin access rights.", "Users with this right have access to performance and insights."], "type": "string"}, "type": "array"}, "name": {"description": "Identifier. The resource name of the user. Format: `accounts/{account}/user/{email}` Use `me` to refer to your own email address, for example `accounts/{account}/users/me`.", "type": "string"}, "state": {"description": "Output only. The state of the user.", "enum": ["STATE_UNSPECIFIED", "PENDING", "VERIFIED"], "enumDescriptions": ["Default value. This value is unused.", "The user is pending confirmation. In this state, the user first needs to accept the invitation before performing other actions.", "The user is verified."], "readOnly": true, "type": "string"}}, "type": "object"}, "Value": {"description": "The single value of a rate group or the value of a rate group table's cell. Exactly one of `no_shipping`, `flat_rate`, `price_percentage`, `carrier_rateName`, `subtable_name` must be set.", "id": "Value", "properties": {"carrierRate": {"description": "The name of a carrier rate referring to a carrier rate defined in the same rate group. Can only be set if all other fields are not set.", "type": "string"}, "flatRate": {"$ref": "Price", "description": "A flat rate. Can only be set if all other fields are not set."}, "noShipping": {"description": "If true, then the product can't be shipped. Must be true when set, can only be set if all other fields are not set.", "type": "boolean"}, "pricePercentage": {"description": "A percentage of the price represented as a number in decimal notation (For example, `\"5.4\"`). Can only be set if all other fields are not set.", "type": "string"}, "subtable": {"description": "The name of a subtable. Can only be set in table cells (For example, not for single values), and only if all other fields are not set.", "type": "string"}}, "type": "object"}, "Warehouse": {"description": "A fulfillment warehouse, which stores and handles inventory.", "id": "Warehouse", "properties": {"businessDayConfig": {"$ref": "BusinessDayConfig", "description": "Business days of the warehouse. If not set, will be Monday to Friday by default."}, "cutoffTime": {"$ref": "WarehouseCutoffTime", "description": "Required. The latest time of day that an order can be accepted and begin processing. Later orders will be processed in the next day. The time is based on the warehouse postal code."}, "handlingDays": {"description": "Required. The number of days it takes for this warehouse to pack up and ship an item. This is on the warehouse level, but can be overridden on the offer level based on the attributes of an item.", "format": "int64", "type": "string"}, "name": {"description": "Required. The name of the warehouse. Must be unique within account.", "type": "string"}, "shippingAddress": {"$ref": "Address", "description": "Required. Shipping address of the warehouse."}}, "type": "object"}, "WarehouseBasedDeliveryTime": {"description": "Indicates that the delivery time should be calculated per warehouse (shipping origin location) based on the settings of the selected carrier. When set, no other transit time related field in `delivery_time` should be set.", "id": "WarehouseBasedDeliveryTime", "properties": {"carrier": {"description": "Required. Carrier, such as `\"UPS\"` or `\"Fedex\"`. [supported carriers](https://support.google.com/merchants/answer/7050921#zippy=%2Ccarrier-rates-au-de-uk-and-us-only)", "type": "string"}, "carrierService": {"description": "Required. Carrier service, such as `\"ground\"` or `\"2 days\"`. The name of the service must be in the eddSupportedServices list.", "type": "string"}, "warehouse": {"description": "Required. Warehouse name. This should match [warehouse](/merchant/api/reference/rest/accounts_v1beta/accounts.shippingSettings#warehouse)", "type": "string"}}, "type": "object"}, "WarehouseCutoffTime": {"description": "The latest time of day that an order can be accepted and begin processing. Later orders will be processed in the next day. The time is based on the warehouse postal code.", "id": "WarehouseCutoffTime", "properties": {"hour": {"description": "Required. Hour of the cutoff time until which an order has to be placed to be processed in the same day by the warehouse. Hour is based on the timezone of warehouse.", "format": "int32", "type": "integer"}, "minute": {"description": "Required. Min<PERSON> of the cutoff time until which an order has to be placed to be processed in the same day by the warehouse. Minute is based on the timezone of warehouse.", "format": "int32", "type": "integer"}}, "type": "object"}, "Weight": {"description": "The weight represented as the value in string and the unit.", "id": "Weight", "properties": {"amountMicros": {"description": "Required. The weight represented as a number in micros (1 million micros is an equivalent to one's currency standard unit, for example, 1 kg = 1000000 micros). This field can also be set as infinity by setting to -1. This field only support -1 and positive value.", "format": "int64", "type": "string"}, "unit": {"description": "Required. The weight unit. Acceptable values are: kg and lb", "enum": ["WEIGHT_UNIT_UNSPECIFIED", "POUND", "KILOGRAM"], "enumDescriptions": ["unit unspecified", "lb unit.", "kg unit."], "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Merchant API", "version": "accounts_v1beta", "version_module": true}