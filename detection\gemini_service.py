"""
Gemini Image Analysis Service for Breast Cancer Detection System
Provides AI-powered image description and analysis using Google's Gemini API
"""

import os
import logging
from pathlib import Path
from datetime import datetime
from django.conf import settings

logger = logging.getLogger(__name__)


class GeminiImageAnalysisService:
    """Service class for Gemini image analysis with safe dependency handling"""
    
    def __init__(self):
        self.model = None
        self.gemini_available = False
        self.api_key = None
        
        # Check Gemini dependencies and API key
        self._check_gemini_dependencies()
        
        # Initialize model if available
        if self.gemini_available:
            self._initialize_model()
    
    def _check_gemini_dependencies(self):
        """Check if Gemini dependencies and API key are available"""
        try:
            import google.generativeai as genai

            # Check for API key in environment variables or settings
            self.api_key = os.getenv('GEMINI_API_KEY') or getattr(settings, 'GEMINI_API_KEY', None)

            if not self.api_key:
                logger.warning("⚠️  Gemini API key not found. Set GEMINI_API_KEY environment variable or in settings.")
                logger.warning("   Image descriptions will not be available.")
                self.gemini_available = False
                return

            self.gemini_available = True
            logger.info("✅ Gemini dependencies and API key available")

        except ImportError as e:
            self.gemini_available = False
            logger.warning(f"⚠️  Gemini dependencies not available: {e}")
            logger.warning("   Image descriptions will not be available.")
    
    def _initialize_model(self):
        """Initialize the Gemini model"""
        if not self.gemini_available:
            return

        try:
            import google.generativeai as genai

            # Configure API key
            genai.configure(api_key=self.api_key)

            # Initialize model
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            logger.info("✅ Gemini model initialized successfully")

        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini model: {str(e)}")
            self.gemini_available = False
    
    def analyze_image(self, image_path, uploaded_image_instance):
        """
        Analyze image using Gemini and return description
        
        Args:
            image_path (str): Path to the image file
            uploaded_image_instance: UploadedImage model instance
            
        Returns:
            dict: Analysis result with description and metadata
        """
        if not self.gemini_available:
            return self._demo_analysis(uploaded_image_instance)
        
        if not self.model:
            return {
                'success': False,
                'error': 'Gemini model not initialized',
                'description': None,
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            # Read image file
            with open(image_path, "rb") as f:
                image_bytes = f.read()
            
            # Determine MIME type based on file extension
            file_extension = Path(image_path).suffix.lower()
            mime_type_map = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.webp': 'image/webp'
            }
            mime_type = mime_type_map.get(file_extension, 'image/jpeg')
            
            # Create prompt for medical image analysis
            prompt = self._create_medical_analysis_prompt()
            
            # Send image and prompt to Gemini
            response = self.model.generate_content([
                {"mime_type": mime_type, "data": image_bytes},
                prompt
            ])
            
            # Extract description from response
            description = response.text if response.text else "No description generated"
            
            analysis_result = {
                'success': True,
                'description': description,
                'model_info': {
                    'model_name': 'gemini-1.5-flash',
                    'mime_type': mime_type,
                    'prompt_type': 'medical_analysis'
                },
                'timestamp': datetime.now().isoformat(),
                'image_info': {
                    'filename': uploaded_image_instance.original_filename,
                    'upload_time': uploaded_image_instance.uploaded_at.isoformat(),
                    'image_id': uploaded_image_instance.id
                }
            }
            
            logger.info(f"✅ Gemini analysis completed for image {uploaded_image_instance.id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ Gemini analysis failed for image {uploaded_image_instance.id}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'description': None,
                'timestamp': datetime.now().isoformat()
            }
    
    def _create_medical_analysis_prompt(self):
        """Create a specialized prompt for medical image analysis"""
        return """
        Please provide a detailed, professional description of this medical image. Focus on:
        
        1. **Visual Characteristics**: Describe what you can observe in the image including:
           - Image quality and clarity
           - Color patterns and variations
           - Texture and structural features
           - Any notable visual patterns or formations
        
        2. **Technical Aspects**: Comment on:
           - Image resolution and quality
           - Lighting and contrast
           - Any artifacts or technical issues
        
        3. **General Observations**: Provide:
           - Overall appearance and composition
           - Any distinctive features or patterns
           - General visual assessment
        
        Please provide a clear, objective description suitable for medical documentation. 
        Do not attempt to make medical diagnoses or provide medical advice.
        Focus on describing what is visually observable in the image.
        
        Keep the description professional, detailed, and informative.
        """
    
    def _demo_analysis(self, uploaded_image_instance):
        """Generate demo analysis when Gemini is not available"""
        import random
        
        demo_descriptions = [
            "High-resolution medical image showing tissue structure with clear definition. The image displays good contrast and lighting conditions, allowing for detailed visual examination of cellular patterns and morphological features.",
            
            "Medical imaging sample with well-defined boundaries and clear tissue differentiation. The image quality is suitable for analysis, showing various textural patterns and structural characteristics typical of histological specimens.",
            
            "Detailed medical image with excellent clarity and resolution. Observable features include distinct cellular arrangements, color variations indicating different tissue types, and well-preserved morphological structures.",
            
            "Professional-grade medical image displaying tissue architecture with good visual contrast. The specimen shows clear definition of structural elements and maintains diagnostic quality throughout the field of view.",
            
            "High-quality histological image with sharp focus and appropriate magnification. The tissue sample demonstrates clear cellular boundaries, distinct color patterns, and well-preserved morphological details suitable for examination."
        ]
        
        description = random.choice(demo_descriptions)
        
        return {
            'success': True,
            'description': description,
            'model_info': {
                'model_name': 'DEMO',
                'mime_type': 'demo',
                'prompt_type': 'demo_medical_analysis'
            },
            'timestamp': datetime.now().isoformat(),
            'image_info': {
                'filename': uploaded_image_instance.original_filename,
                'upload_time': uploaded_image_instance.uploaded_at.isoformat(),
                'image_id': uploaded_image_instance.id
            },
            'demo_mode': True
        }
    
    def get_service_status(self):
        """Get current service status"""
        return {
            'gemini_available': self.gemini_available,
            'model_initialized': self.model is not None,
            'api_key_configured': self.api_key is not None
        }


# Create global service instance
gemini_service = GeminiImageAnalysisService()
